<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Traits\CreatesApplication;
use Tests\Traits\InteractsWithAuthentication;
use Tests\Traits\InteractsWithDatabase;
use Tests\Traits\InteractsWithExternalServices;

/**
 * Enhanced Base TestCase
 * Tüm test sınıfları için gelişmiş base class
 */
abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;
    use RefreshDatabase;
    use WithFaker;
    use InteractsWithAuthentication;
    use InteractsWithDatabase;
    use InteractsWithExternalServices;

    /**
     * Test başlangıcında çalışacak setup
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Faker locale'ini Türkçe yap
        $this->faker = \Faker\Factory::create('tr_TR');

        // Test environment setup
        $this->setupTestEnvironment();

        // Mock external services if enabled
        if (config('testing.mock_external_services', true)) {
            $this->mockExternalServices();
        }
    }

    /**
     * Test sonunda çalışacak teardown
     */
    protected function tearDown(): void
    {
        $this->cleanupTestEnvironment();
        parent::tearDown();
    }

    /**
     * Test environment'ını setup et
     */
    protected function setupTestEnvironment(): void
    {
        // Test config'lerini yükle
        $this->setupTestConfigs();

        // Database transaction'ları kontrol et
        $this->setupDatabaseTransactions();

        // Cache'i temizle
        \Cache::flush();

        // Event listener'ları temizle
        \Event::fake();

        // Queue'yu sync yap
        \Queue::fake();

        // Mail'i fake yap
        \Mail::fake();

        // Notification'ları fake yap
        \Notification::fake();
    }

    /**
     * Database transaction'ları setup et
     */
    protected function setupDatabaseTransactions(): void
    {
        try {
            // Eğer aktif transaction varsa rollback yap
            while (\DB::transactionLevel() > 0) {
                \DB::rollBack();
            }
        } catch (\Exception $e) {
            // Transaction rollback hatası varsa ignore et
        }

        // Test için fresh transaction başlat
        if (property_exists($this, 'useTransactions') && $this->useTransactions) {
            try {
                \DB::beginTransaction();
            } catch (\Exception $e) {
                // Transaction başlatma hatası varsa ignore et
            }
        }
    }

    /**
     * Test config'lerini setup et
     */
    protected function setupTestConfigs(): void
    {
        // Test environment için gerekli config'leri set et
        config([
            'testing.mock_external_services' => true,
            'cqrs.command_handlers' => [],
            'cqrs.query_handlers' => [],
            'modules.enabled' => ['Products', 'Categories', 'Orders', 'Users', 'Payments'],
            'features' => [],
        ]);
    }

    /**
     * Test environment'ını temizle
     */
    protected function cleanupTestEnvironment(): void
    {
        // Database transaction'ları temizle
        $this->cleanupDatabaseTransactions();

        // Temporary files cleanup
        $this->cleanupTemporaryFiles();

        // Memory cleanup
        gc_collect_cycles();
    }

    /**
     * Database transaction'ları temizle
     */
    protected function cleanupDatabaseTransactions(): void
    {
        try {
            // Tüm aktif transaction'ları rollback yap
            while (\DB::transactionLevel() > 0) {
                \DB::rollBack();
            }
        } catch (\Exception $e) {
            // Transaction cleanup hatası varsa ignore et
        }
    }

    /**
     * Temporary dosyaları temizle
     */
    protected function cleanupTemporaryFiles(): void
    {
        $tempDir = storage_path('testing/temp');
        if (is_dir($tempDir)) {
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Test assertion helper'ları
     */

    /**
     * Domain event'in dispatch edildiğini assert et
     */
    protected function assertDomainEventDispatched(string $eventClass, callable $callback = null): void
    {
        $events = app('events')->getListeners($eventClass);
        $this->assertNotEmpty($events, "Domain event {$eventClass} was not dispatched");

        if ($callback) {
            $this->assertTrue($callback(), "Domain event {$eventClass} callback assertion failed");
        }
    }

    /**
     * API response structure'ını assert et
     */
    protected function assertApiResponseStructure(array $structure, $response): void
    {
        if (is_object($response) && method_exists($response, 'json')) {
            $response->assertJsonStructure($structure);
        } else {
            $this->assertArrayHasKey('data', $response);
            $this->assertArrayHasKey('meta', $response);
            $this->assertArrayHasKey('success', $response);
        }
    }

    /**
     * Clean Architecture layer'larını assert et
     */
    protected function assertCleanArchitectureCompliance(string $className): void
    {
        $reflection = new \ReflectionClass($className);

        // Domain layer dependencies kontrolü
        if (str_contains($className, '\\Domain\\')) {
            $this->assertDomainLayerCompliance($reflection);
        }

        // Application layer dependencies kontrolü
        if (str_contains($className, '\\Application\\')) {
            $this->assertApplicationLayerCompliance($reflection);
        }

        // Infrastructure layer dependencies kontrolü
        if (str_contains($className, '\\Infrastructure\\')) {
            $this->assertInfrastructureLayerCompliance($reflection);
        }
    }

    /**
     * Domain layer compliance kontrolü
     */
    private function assertDomainLayerCompliance(\ReflectionClass $reflection): void
    {
        $dependencies = $this->getClassDependencies($reflection);

        foreach ($dependencies as $dependency) {
            // Domain layer sadece kendi içindeki ve shared bileşenleri kullanabilir
            $this->assertFalse(
                str_contains($dependency, '\\Infrastructure\\') ||
                str_contains($dependency, '\\Application\\'),
                "Domain layer class {$reflection->getName()} has invalid dependency: {$dependency}"
            );
        }
    }

    /**
     * Application layer compliance kontrolü
     */
    private function assertApplicationLayerCompliance(\ReflectionClass $reflection): void
    {
        $dependencies = $this->getClassDependencies($reflection);

        foreach ($dependencies as $dependency) {
            // Application layer Infrastructure'a direct dependency olmamalı
            $this->assertFalse(
                str_contains($dependency, '\\Infrastructure\\'),
                "Application layer class {$reflection->getName()} has invalid dependency: {$dependency}"
            );
        }
    }

    /**
     * Infrastructure layer compliance kontrolü
     */
    private function assertInfrastructureLayerCompliance(\ReflectionClass $reflection): void
    {
        // Infrastructure layer her şeyi kullanabilir, özel kısıtlama yok
        $this->assertTrue(true);
    }

    /**
     * Class dependencies'leri al
     */
    private function getClassDependencies(\ReflectionClass $reflection): array
    {
        $dependencies = [];

        // Constructor dependencies
        $constructor = $reflection->getConstructor();
        if ($constructor) {
            foreach ($constructor->getParameters() as $parameter) {
                $type = $parameter->getType();
                if ($type && !$type->isBuiltin()) {
                    $dependencies[] = $type->getName();
                }
            }
        }

        // Use statements (basit parsing)
        $source = file_get_contents($reflection->getFileName());
        preg_match_all('/use\s+([^;]+);/', $source, $matches);

        return array_merge($dependencies, $matches[1] ?? []);
    }
}
