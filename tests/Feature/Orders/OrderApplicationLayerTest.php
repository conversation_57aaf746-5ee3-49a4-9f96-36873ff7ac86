<?php

namespace Tests\Feature\Orders;

use Tests\TestCase;
use App\Application\Orders\Services\OrderApplicationService;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Entities\Order;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\Address;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class OrderApplicationLayerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private OrderApplicationService $orderService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderService = app(OrderApplicationService::class);
    }

    public function test_it_can_create_order_through_application_service(): void
    {
        // Arrange
        $user = User::factory()->create();
        $items = [
            [
                'product_id' => 1,
                'product_name' => 'Test Product',
                'price' => 99.99,
                'quantity' => 2,
                'options' => []
            ]
        ];

        // Act
        $orderDTO = $this->orderService->createOrder(
            userId: $user->id,
            items: $items,
            paymentMethod: 'credit_card',
            shippingMethod: 'standard'
        );

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $orderDTO);
        $this->assertEquals($user->id, $orderDTO->userId);
        $this->assertEquals(OrderStatus::PENDING, $orderDTO->status);
        $this->assertEquals(PaymentStatus::PENDING, $orderDTO->paymentStatus);
        $this->assertCount(1, $orderDTO->items);
        $this->assertDatabaseHas('orders', [
            'user_id' => $user->id,
            'status' => OrderStatus::PENDING->value
        ]);
    }

    public function test_it_can_add_note_to_order(): void
    {
        // Arrange
        $user = User::factory()->create();
        $order = $this->createTestOrder($user->id);
        $noteText = "Test note for order";

        // Act
        $orderDTO = $this->orderService->addOrderNote(
            orderId: $order->id,
            note: $noteText,
            isPublic: true,
            userId: $user->id,
            type: 'customer'
        );

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $orderDTO);
        $this->assertCount(1, $orderDTO->notes);
        $this->assertEquals($noteText, $orderDTO->notes[0]->note);
        $this->assertTrue($orderDTO->notes[0]->isPublic);
    }

    public function test_it_can_update_order_shipping_information(): void
    {
        // Arrange
        $user = User::factory()->create();
        $order = $this->createTestOrder($user->id);
        $trackingNumber = 'TRK123456789';
        $shippingCompany = 'DHL';
        $shippingDate = Carbon::now();

        // Act
        $orderDTO = $this->orderService->updateOrderShipping(
            orderId: $order->id,
            trackingNumber: $trackingNumber,
            shippingCompany: $shippingCompany,
            shippingDate: $shippingDate
        );

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $orderDTO);
        $this->assertEquals($trackingNumber, $orderDTO->trackingNumber);
        $this->assertEquals($shippingCompany, $orderDTO->shippingCompany);
        $this->assertEquals($shippingDate->toISOString(), $orderDTO->shippingDate);
    }

    public function test_it_can_process_refund(): void
    {
        // Arrange
        $user = User::factory()->create();
        $order = $this->createTestOrder($user->id, OrderStatus::DELIVERED);
        $refundAmount = 50.0;
        $reason = "Product defect";

        // Act
        $orderDTO = $this->orderService->processRefund(
            orderId: $order->id,
            refundAmount: $refundAmount,
            reason: $reason,
            isPartialRefund: true
        );

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $orderDTO);
        $this->assertEquals(PaymentStatus::PARTIALLY_REFUNDED, $orderDTO->paymentStatus);
        $this->assertGreaterThan(0, count($orderDTO->notes));
    }

    public function test_it_can_bulk_update_orders(): void
    {
        // Arrange
        $user = User::factory()->create();
        $order1 = $this->createTestOrder($user->id);
        $order2 = $this->createTestOrder($user->id);
        $orderIds = [$order1->id, $order2->id];
        $newStatus = OrderStatus::CONFIRMED;
        $note = "Bulk update test";

        // Act
        $result = $this->orderService->bulkUpdateOrders(
            orderIds: $orderIds,
            newStatus: $newStatus,
            note: $note
        );

        // Assert
        $this->assertEquals(2, $result['total']);
        $this->assertEquals(2, $result['updated']);
        $this->assertCount(2, $result['success']);
        $this->assertEmpty($result['failed']);
        
        $this->assertDatabaseHas('orders', [
            'id' => $order1->id,
            'status' => $newStatus->value
        ]);
        $this->assertDatabaseHas('orders', [
            'id' => $order2->id,
            'status' => $newStatus->value
        ]);
    }

    public function test_it_can_get_order_statistics(): void
    {
        // Arrange
        $user = User::factory()->create();
        $this->createTestOrder($user->id, OrderStatus::DELIVERED);
        $this->createTestOrder($user->id, OrderStatus::PENDING);
        $this->createTestOrder($user->id, OrderStatus::CANCELLED);

        // Act
        $statistics = $this->orderService->getOrderStatistics(
            startDate: Carbon::now()->subDays(7)->toDateString(),
            endDate: Carbon::now()->toDateString(),
            includeStatusBreakdown: true,
            includePaymentBreakdown: true
        );

        // Assert
        $this->assertIsArray($statistics);
        $this->assertArrayHasKey('summary', $statistics);
        $this->assertArrayHasKey('status_breakdown', $statistics);
        $this->assertArrayHasKey('payment_breakdown', $statistics);
        $this->assertGreaterThan(0, $statistics['summary']['total_orders']);
    }

    public function test_it_can_get_orders_by_advanced_date_range(): void
    {
        // Arrange
        $user = User::factory()->create();
        $this->createTestOrder($user->id);
        $this->createTestOrder($user->id);

        $startDate = Carbon::now()->subDays(1);
        $endDate = Carbon::now()->addDays(1);

        // Act
        $result = $this->orderService->getOrdersByAdvancedDateRange(
            startDate: $startDate,
            endDate: $endDate,
            userId: $user->id,
            limit: 10
        );

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertGreaterThan(0, $result['total']);
        $this->assertCount(2, $result['data']);
    }

    public function test_it_can_generate_order_report(): void
    {
        // Arrange
        $user = User::factory()->create();
        $this->createTestOrder($user->id, OrderStatus::DELIVERED);
        $this->createTestOrder($user->id, OrderStatus::PENDING);

        // Act
        $report = $this->orderService->getOrderReport(
            reportType: 'daily',
            startDate: Carbon::now()->subDays(7),
            endDate: Carbon::now(),
            metrics: ['orders', 'revenue'],
            includeChartData: true
        );

        // Assert
        $this->assertIsArray($report);
        $this->assertArrayHasKey('report_info', $report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('data', $report);
        $this->assertArrayHasKey('chart_data', $report);
        $this->assertEquals('daily', $report['report_info']['type']);
    }

    private function createTestOrder(int $userId, OrderStatus $status = OrderStatus::PENDING): object
    {
        $orderData = [
            'user_id' => $userId,
            'order_number' => 'ORD-' . time() . '-' . rand(1000, 9999),
            'subtotal' => 199.98,
            'total' => 199.98,
            'currency' => 'TRY',
            'shipping_cost' => 0,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'status' => $status->value,
            'payment_status' => PaymentStatus::PENDING->value,
            'payment_method' => 'credit_card',
            'shipping_method' => 'standard',
            'billing_address' => json_encode(['address' => 'Test Address']),
            'shipping_address' => json_encode(['address' => 'Test Address']),
            'created_at' => now(),
            'updated_at' => now()
        ];

        $orderId = \DB::table('orders')->insertGetId($orderData);

        // Return object with id property
        return (object) ['id' => $orderId];
    }
}
