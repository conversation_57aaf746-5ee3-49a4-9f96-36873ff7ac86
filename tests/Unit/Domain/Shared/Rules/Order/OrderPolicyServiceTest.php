<?php

namespace Tests\Unit\Domain\Shared\Rules\Order;

use Tests\TestCase;
use App\Domain\Shared\Rules\Order\OrderPolicyService;
use App\Domain\Shared\Rules\Order\OrderValidationRule;
use App\Domain\Shared\Rules\Order\OrderStatusTransitionRule;
use App\Domain\Shared\Rules\Order\OrderCancellationRule;
use App\Domain\Orders\Entities\Order;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Entities\OrderItem;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Customers\Entities\Customer;

class OrderPolicyServiceTest extends TestCase
{
    private OrderPolicyService $service;
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new OrderPolicyService();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_can_validate_order()
    {
        $result = $this->service->validateOrder($this->testOrder);
        
        $this->assertNotNull($result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isAllowed());
        $this->assertEquals($this->testOrder, $result->getOrder());
        $this->assertEquals('validate', $result->getAction());
    }

    public function test_can_validate_status_transition()
    {
        $targetStatus = 'processing';
        
        $result = $this->service->validateStatusTransition($this->testOrder, $targetStatus);
        
        $this->assertNotNull($result);
        $this->assertEquals($this->testOrder, $result->getOrder());
        $this->assertEquals('status_transition', $result->getAction());
        $this->assertEquals($targetStatus, $result->getTargetStatus());
    }

    public function test_can_validate_cancellation()
    {
        $reason = 'Customer request';
        
        $result = $this->service->validateCancellation($this->testOrder, $reason);
        
        $this->assertNotNull($result);
        $this->assertEquals($this->testOrder, $result->getOrder());
        $this->assertEquals('cancel', $result->getAction());
    }

    public function test_can_register_and_unregister_rules()
    {
        $customRule = OrderValidationRule::express();
        
        $this->service->registerRule($customRule);
        $rules = $this->service->getRules();
        $this->assertCount(6, $rules); // 5 standart + 1 custom

        $this->service->unregisterRule('order_validation');
        $rules = $this->service->getRules();
        $this->assertCount(5, $rules);
    }

    public function test_can_get_rules_by_priority()
    {
        $rules = $this->service->getRulesByPriority();
        
        $this->assertIsArray($rules);
        $this->assertCount(5, $rules);
        
        // Öncelik sırasını kontrol et
        $priorities = array_map(function ($rule) {
            return $rule->getPriority();
        }, $rules);
        
        $sortedPriorities = $priorities;
        rsort($sortedPriorities);
        
        $this->assertEquals($sortedPriorities, $priorities);
    }

    public function test_can_get_specific_rule()
    {
        $rule = $this->service->getRule('order_validation');
        
        $this->assertNotNull($rule);
        $this->assertInstanceOf(OrderValidationRule::class, $rule);
        $this->assertEquals('order_validation', $rule->getName());
    }

    public function test_can_check_rule_existence()
    {
        $this->assertTrue($this->service->hasRule('order_validation'));
        $this->assertTrue($this->service->hasRule('order_status_transition'));
        $this->assertTrue($this->service->hasRule('order_cancellation'));
        $this->assertFalse($this->service->hasRule('non_existent_rule'));
    }

    public function test_can_clear_and_reload_rules()
    {
        $this->assertCount(5, $this->service->getRules());

        $this->service->clearRules();
        $this->assertCount(0, $this->service->getRules());

        $this->service->reloadStandardRules();
        $this->assertCount(5, $this->service->getRules());
    }

    public function test_validates_order_with_insufficient_amount()
    {
        $this->testOrder->setTotalAmount(Money::fromAmount(5, 'TRY')); // Minimum altında
        
        $result = $this->service->validateOrder($this->testOrder);
        
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isAllowed());
        $this->assertNotEmpty($result->getErrors());
    }

    public function test_validates_order_without_items()
    {
        $this->testOrder->getItems()->clear();
        
        $result = $this->service->validateOrder($this->testOrder);
        
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isAllowed());
        $this->assertContains('must contain at least one item', $result->getErrors());
    }

    public function test_validates_status_transition_invalid()
    {
        $this->testOrder->setStatus('delivered');
        
        $result = $this->service->validateStatusTransition($this->testOrder, 'pending');
        
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isAllowed());
        $this->assertNotEmpty($result->getErrors());
    }

    public function test_validates_cancellation_for_shipped_order()
    {
        $this->testOrder->setStatus('shipped');
        
        $result = $this->service->validateCancellation($this->testOrder, 'Customer request');
        
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isAllowed());
        $this->assertNotEmpty($result->getErrors());
    }

    public function test_order_requires_actions()
    {
        // Ödeme yöntemi olmayan sipariş
        $this->testOrder->setPaymentMethod(null);
        
        $result = $this->service->validateOrder($this->testOrder);
        
        $this->assertTrue($result->requiresActions());
        $this->assertContains('payment_method_selection', $result->getRequiredActions());
    }

    public function test_can_get_rule_result_by_name()
    {
        $result = $this->service->validateOrder($this->testOrder);
        
        $validationResult = $result->getRuleResult('order_validation');
        $this->assertNotNull($validationResult);
        $this->assertEquals('order_validation', $validationResult->getRuleName());
    }

    public function test_result_to_array()
    {
        $result = $this->service->validateOrder($this->testOrder);
        $array = $result->toArray();
        
        $this->assertIsArray($array);
        $this->assertArrayHasKey('order_id', $array);
        $this->assertArrayHasKey('action', $array);
        $this->assertArrayHasKey('is_valid', $array);
        $this->assertArrayHasKey('is_allowed', $array);
        $this->assertArrayHasKey('requires_actions', $array);
        $this->assertArrayHasKey('rule_results', $array);
        
        $this->assertEquals($this->testOrder->getId(), $array['order_id']);
        $this->assertEquals('validate', $array['action']);
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');
        $customer->setEmailVerified(true);
        $customer->setPhoneVerified(true);

        $order = new Order(
            1, // userId
            new OrderNumber('ORD-2024-001'),
            new Money(200.00, 'TRY'),
            'credit_card',
            'standard_shipping'
        );
        $order->setId(1);
        $order->setCustomer($customer);
        $order->setStatus('processing');
        $order->setTotalAmount(Money::fromAmount(200, 'TRY'));
        $order->setPaymentMethod('credit_card');
        $order->setPaid(true);
        $order->setCreatedAt(now());

        // Test için shipping address ekle
        $address = new Address(
            'John Doe',
            '+905551234567',
            'Test Street 123',
            'Istanbul',
            'Istanbul',
            'Turkey',
            'shipping'
        );
        $order->setShippingAddress($address);

        // Test için items ekle
        $order->addItem($this->createTestOrderItem());

        return $order;
    }

    private function createTestOrderItem()
    {
        return new OrderItem(
            1, // productId
            'Test Product',
            Money::fromAmount(50, 'TRY'),
            2 // quantity
        );
    }

    private function createTestProduct()
    {
        // Product mock'u oluştur
        $product = new \stdClass();
        $product->id = 1;
        $product->name = 'Test Product';
        $product->stock_quantity = 10;
        $product->is_active = true;
        
        return $product;
    }
}
