##teamcity[testCount count='627' flowId='19088']
##teamcity[testSuiteStarted name='CLI Arguments' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest' flowId='19088']
##teamcity[testStarted name='repository_can_delete_order' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_delete_order' flowId='19088']
##teamcity[testFinished name='repository_can_delete_order' duration='21' flowId='19088']
##teamcity[testStarted name='it_can_resolve_order_repository_from_container' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::it_can_resolve_order_repository_from_container' flowId='19088']
##teamcity[testFinished name='it_can_resolve_order_repository_from_container' duration='3' flowId='19088']
##teamcity[testStarted name='repository_can_check_existence' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_check_existence' flowId='19088']
##teamcity[testFinished name='repository_can_check_existence' duration='4' flowId='19088']
##teamcity[testStarted name='status_service_can_update_order_status' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::status_service_can_update_order_status' flowId='19088']
##teamcity[testFinished name='status_service_can_update_order_status' duration='20' flowId='19088']
##teamcity[testStarted name='repository_can_count_orders' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_count_orders' flowId='19088']
##teamcity[testFinished name='repository_can_count_orders' duration='4' flowId='19088']
##teamcity[testStarted name='status_service_can_get_status_statistics' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::status_service_can_get_status_statistics' flowId='19088']
##teamcity[testFinished name='status_service_can_get_status_statistics' duration='6' flowId='19088']
##teamcity[testStarted name='notification_service_can_send_status_change_notification' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::notification_service_can_send_status_change_notification' flowId='19088']
##teamcity[testFinished name='notification_service_can_send_status_change_notification' duration='3' flowId='19088']
##teamcity[testStarted name='notification_service_can_send_order_created_notification' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::notification_service_can_send_order_created_notification' flowId='19088']
##teamcity[testFinished name='notification_service_can_send_order_created_notification' duration='3' flowId='19088']
##teamcity[testStarted name='it_can_resolve_order_services_from_container' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::it_can_resolve_order_services_from_container' flowId='19088']
##teamcity[testFinished name='it_can_resolve_order_services_from_container' duration='4' flowId='19088']
##teamcity[testStarted name='repository_can_find_orders_by_user_id' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_find_orders_by_user_id' flowId='19088']
##teamcity[testFinished name='repository_can_find_orders_by_user_id' duration='7' flowId='19088']
##teamcity[testStarted name='repository_can_find_order_by_order_number' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_find_order_by_order_number' flowId='19088']
##teamcity[testFinished name='repository_can_find_order_by_order_number' duration='6' flowId='19088']
##teamcity[testStarted name='status_service_can_perform_bulk_status_update' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::status_service_can_perform_bulk_status_update' flowId='19088']
##teamcity[testFinished name='status_service_can_perform_bulk_status_update' duration='10' flowId='19088']
##teamcity[testStarted name='repository_can_find_orders_by_status' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_find_orders_by_status' flowId='19088']
##teamcity[testFinished name='repository_can_find_orders_by_status' duration='7' flowId='19088']
##teamcity[testStarted name='repository_can_save_and_retrieve_order' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php::\Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest::repository_can_save_and_retrieve_order' flowId='19088']
##teamcity[testFinished name='repository_can_save_and_retrieve_order' duration='6' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Products\Services\PricingDomainServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest' flowId='19088']
##teamcity[testStarted name='it_calculates_bulk_price_with_discount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_bulk_price_with_discount' flowId='19088']
##teamcity[testFinished name='it_calculates_bulk_price_with_discount' duration='6' flowId='19088']
##teamcity[testStarted name='it_calculates_current_price_without_sale' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_current_price_without_sale' flowId='19088']
##teamcity[testFinished name='it_calculates_current_price_without_sale' duration='3' flowId='19088']
##teamcity[testStarted name='it_finds_lowest_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_finds_lowest_price' flowId='19088']
##teamcity[testFinished name='it_finds_lowest_price' duration='3' flowId='19088']
##teamcity[testStarted name='it_compares_prices_correctly' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_compares_prices_correctly' flowId='19088']
##teamcity[testFinished name='it_compares_prices_correctly' duration='4' flowId='19088']
##teamcity[testStarted name='it_calculates_tax_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_tax_amount' flowId='19088']
##teamcity[testFinished name='it_calculates_tax_amount' duration='3' flowId='19088']
##teamcity[testStarted name='it_checks_if_price_is_in_range' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_checks_if_price_is_in_range' flowId='19088']
##teamcity[testFinished name='it_checks_if_price_is_in_range' duration='5' flowId='19088']
##teamcity[testStarted name='it_finds_highest_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_finds_highest_price' flowId='19088']
##teamcity[testFinished name='it_finds_highest_price' duration='3' flowId='19088']
##teamcity[testStarted name='it_calculates_discount_percentage' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_discount_percentage' flowId='19088']
##teamcity[testFinished name='it_calculates_discount_percentage' duration='6' flowId='19088']
##teamcity[testStarted name='it_calculates_discount_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_discount_amount' flowId='19088']
##teamcity[testFinished name='it_calculates_discount_amount' duration='3' flowId='19088']
##teamcity[testStarted name='it_calculates_variant_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_variant_price' flowId='19088']
##teamcity[testFinished name='it_calculates_variant_price' duration='6' flowId='19088']
##teamcity[testStarted name='it_calculates_price_with_tax' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_price_with_tax' flowId='19088']
##teamcity[testFinished name='it_calculates_price_with_tax' duration='4' flowId='19088']
##teamcity[testStarted name='it_calculates_average_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_average_price' flowId='19088']
##teamcity[testFinished name='it_calculates_average_price' duration='3' flowId='19088']
##teamcity[testStarted name='it_calculates_current_price_with_active_sale' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_current_price_with_active_sale' flowId='19088']
##teamcity[testFinished name='it_calculates_current_price_with_active_sale' duration='4' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Products\Services\PricingDomainServiceTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php::\Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest' flowId='19088']
##teamcity[testStarted name='test_cannot_create_payment_for_existing_order' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php::\Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest::test_cannot_create_payment_for_existing_order' flowId='19088']
##teamcity[testFinished name='test_cannot_create_payment_for_existing_order' duration='6' flowId='19088']
##teamcity[testStarted name='test_can_create_payment' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php::\Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest::test_can_create_payment' flowId='19088']
##teamcity[testFailed name='test_can_create_payment' message='Illuminate\Contracts\Container\BindingResolutionException: Target class |[config|] does not exist.' details='C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:1019|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:890|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Foundation\Application.php:1077|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:821|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Foundation\Application.php:1057|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:1653|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\DatabaseManager.php:388|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\DatabaseManager.php:93|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1918|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1884|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1664|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1583|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1619|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1572|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:2449|nC:\laragon\www\modularecommerce\app\Domain\Payment\Entities\Payment.php:95|nC:\laragon\www\modularecommerce\app\Domain\Payment\Entities\Payment.php:112|nC:\laragon\www\modularecommerce\app\Domain\Payment\Services\PaymentDomainService.php:70|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php:72|n|nCaused by|nReflectionException: Class "config" does not exist|n|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:1017|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:890|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Foundation\Application.php:1077|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:821|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Foundation\Application.php:1057|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:1653|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\DatabaseManager.php:388|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\DatabaseManager.php:93|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1918|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1884|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1664|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1583|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1619|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1572|nC:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:2449|nC:\laragon\www\modularecommerce\app\Domain\Payment\Entities\Payment.php:95|nC:\laragon\www\modularecommerce\app\Domain\Payment\Entities\Payment.php:112|nC:\laragon\www\modularecommerce\app\Domain\Payment\Services\PaymentDomainService.php:70|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php:72|n' duration='12' flowId='19088']
##teamcity[testFinished name='test_can_create_payment' duration='13' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\DatabaseTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\DatabaseTest.php::\Tests\Unit\DatabaseTest' flowId='19088']
##teamcity[testStarted name='test_database_with_migrations' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\DatabaseTest.php::\Tests\Unit\DatabaseTest::test_database_with_migrations' flowId='19088']
##teamcity[testFinished name='test_database_with_migrations' duration='5' flowId='19088']
##teamcity[testStarted name='test_user_creation' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\DatabaseTest.php::\Tests\Unit\DatabaseTest::test_user_creation' flowId='19088']
##teamcity[testFinished name='test_user_creation' duration='11' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\DatabaseTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Infrastructure\ProductRepositoryTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest' flowId='19088']
##teamcity[testStarted name='it_can_find_product_by_id' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_find_product_by_id' flowId='19088']
##teamcity[testFinished name='it_can_find_product_by_id' duration='13' flowId='19088']
##teamcity[testStarted name='it_can_find_product_by_sku' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_find_product_by_sku' flowId='19088']
##teamcity[testFinished name='it_can_find_product_by_sku' duration='4' flowId='19088']
##teamcity[testStarted name='it_can_count_products' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_count_products' flowId='19088']
##teamcity[testFinished name='it_can_count_products' duration='4' flowId='19088']
##teamcity[testStarted name='it_can_check_if_product_exists' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_check_if_product_exists' flowId='19088']
##teamcity[testFinished name='it_can_check_if_product_exists' duration='4' flowId='19088']
##teamcity[testStarted name='it_can_find_products_by_category' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_find_products_by_category' flowId='19088']
##teamcity[testFinished name='it_can_find_products_by_category' duration='7' flowId='19088']
##teamcity[testStarted name='it_returns_null_when_product_not_found' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_returns_null_when_product_not_found' flowId='19088']
##teamcity[testFinished name='it_returns_null_when_product_not_found' duration='4' flowId='19088']
##teamcity[testStarted name='it_can_save_a_product' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_save_a_product' flowId='19088']
##teamcity[testFinished name='it_can_save_a_product' duration='4' flowId='19088']
##teamcity[testStarted name='it_can_find_product_by_slug' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_find_product_by_slug' flowId='19088']
##teamcity[testFinished name='it_can_find_product_by_slug' duration='5' flowId='19088']
##teamcity[testStarted name='it_can_find_active_products' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_find_active_products' flowId='19088']
##teamcity[testFinished name='it_can_find_active_products' duration='5' flowId='19088']
##teamcity[testStarted name='it_can_get_statistics' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php::\Tests\Unit\Infrastructure\ProductRepositoryTest::it_can_get_statistics' flowId='19088']
##teamcity[testFinished name='it_can_get_statistics' duration='5' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Infrastructure\ProductRepositoryTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest' flowId='19088']
##teamcity[testStarted name='test_can_create_amount_only_refund' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_create_amount_only_refund' flowId='19088']
##teamcity[testFinished name='test_can_create_amount_only_refund' duration='5' flowId='19088']
##teamcity[testStarted name='test_can_create_full_refund' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_create_full_refund' flowId='19088']
##teamcity[testFinished name='test_can_create_full_refund' duration='3' flowId='19088']
##teamcity[testStarted name='test_invalid_percentage_throws_exception' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_invalid_percentage_throws_exception' flowId='19088']
##teamcity[testFinished name='test_invalid_percentage_throws_exception' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_calculate_net_refund_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_calculate_net_refund_amount' flowId='19088']
##teamcity[testFinished name='test_can_calculate_net_refund_amount' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_calculate_remaining_amounts' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_calculate_remaining_amounts' flowId='19088']
##teamcity[testFinished name='test_can_calculate_remaining_amounts' duration='3' flowId='19088']
##teamcity[testStarted name='test_refund_amount_exceeding_original_throws_exception' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_refund_amount_exceeding_original_throws_exception' flowId='19088']
##teamcity[testFinished name='test_refund_amount_exceeding_original_throws_exception' duration='3' flowId='19088']
##teamcity[testStarted name='test_can_create_refund_by_percentage' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_create_refund_by_percentage' flowId='19088']
##teamcity[testFinished name='test_can_create_refund_by_percentage' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_create_fee_only_refund' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_create_fee_only_refund' flowId='19088']
##teamcity[testFinished name='test_can_create_fee_only_refund' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_check_if_zero' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_check_if_zero' flowId='19088']
##teamcity[testFinished name='test_can_check_if_zero' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_convert_to_array' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_convert_to_array' flowId='19088']
##teamcity[testFinished name='test_can_convert_to_array' duration='3' flowId='19088']
##teamcity[testStarted name='test_currency_mismatch_throws_exception' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_currency_mismatch_throws_exception' flowId='19088']
##teamcity[testFinished name='test_currency_mismatch_throws_exception' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_create_partial_refund' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_create_partial_refund' flowId='19088']
##teamcity[testFinished name='test_can_create_partial_refund' duration='4' flowId='19088']
##teamcity[testStarted name='test_can_convert_to_string' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_convert_to_string' flowId='19088']
##teamcity[testFinished name='test_can_convert_to_string' duration='5' flowId='19088']
##teamcity[testStarted name='test_can_get_type_display_name' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_get_type_display_name' flowId='19088']
##teamcity[testFinished name='test_can_get_type_display_name' duration='3' flowId='19088']
##teamcity[testStarted name='test_can_increase_refund_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_increase_refund_amount' flowId='19088']
##teamcity[testFinished name='test_can_increase_refund_amount' duration='3' flowId='19088']
##teamcity[testStarted name='test_can_increase_refund_fee' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php::\Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest::test_can_increase_refund_fee' flowId='19088']
##teamcity[testFinished name='test_can_increase_refund_fee' duration='5' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\CQRS\CommandBusTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest' flowId='19088']
##teamcity[testStarted name='it_can_dispatch_command' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_can_dispatch_command' flowId='19088']
##teamcity[testFinished name='it_can_dispatch_command' duration='23' flowId='19088']
##teamcity[testStarted name='it_can_add_middleware' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_can_add_middleware' flowId='19088']
##teamcity[testFinished name='it_can_add_middleware' duration='4' flowId='19088']
##teamcity[testStarted name='it_throws_exception_for_unregistered_command' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_throws_exception_for_unregistered_command' flowId='19088']
##teamcity[testFinished name='it_throws_exception_for_unregistered_command' duration='4' flowId='19088']
##teamcity[testStarted name='it_can_register_command_handler' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_can_register_command_handler' flowId='19088']
##teamcity[testFinished name='it_can_register_command_handler' duration='4' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\CQRS\CommandBusTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' flowId='19088']
##teamcity[testFailed name='test_denies_cancellation_from_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='0' flowId='19088']
##teamcity[testFailed name='test_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='195' flowId='19088']
##teamcity[testFailed name='test_denies_cancellation_from_shipped' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='361' flowId='19088']
##teamcity[testFailed name='test_allows_cancellation_from_confirmed' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='546' flowId='19088']
##teamcity[testFailed name='test_denies_shipping_without_address' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='710' flowId='19088']
##teamcity[testFailed name='test_requires_approval_for_specific_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='871' flowId='19088']
##teamcity[testFailed name='test_allows_transition_with_approval' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1041' flowId='19088']
##teamcity[testFailed name='test_denies_processing_without_stock' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1197' flowId='19088']
##teamcity[testFailed name='test_can_set_restricted_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1359' flowId='19088']
##teamcity[testFailed name='test_allows_cancellation_from_pending' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1585' flowId='19088']
##teamcity[testFailed name='test_allows_valid_transition_shipped_to_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1780' flowId='19088']
##teamcity[testFailed name='test_allows_refund_from_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1938' flowId='19088']
##teamcity[testFailed name='test_denies_transition_for_non_order_entity' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='2093' flowId='19088']
##teamcity[testFailed name='test_allows_valid_transition_delivered_to_completed' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='2252' flowId='19088']
##teamcity[testFailed name='test_denies_refund_after_period_expired' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='2417' flowId='19088']
##teamcity[testFailed name='test_standard_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='2588' flowId='19088']
##teamcity[testFailed name='test_strict_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='2732' flowId='19088']
##teamcity[testFailed name='test_denies_refund_from_non_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='2926' flowId='19088']
##teamcity[testFailed name='test_denies_confirmation_without_payment' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='3073' flowId='19088']
##teamcity[testFailed name='test_allows_valid_transition_confirmed_to_processing' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='3223' flowId='19088']
##teamcity[testFailed name='test_returns_next_allowed_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='3368' flowId='19088']
##teamcity[testFailed name='test_denies_transition_without_target_status' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='3554' flowId='19088']
##teamcity[testFailed name='test_allows_valid_transition_pending_to_confirmed' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='3723' flowId='19088']
##teamcity[testFailed name='test_allows_same_status_transition' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='3884' flowId='19088']
##teamcity[testFailed name='test_is_applicable_with_target_status' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='4099' flowId='19088']
##teamcity[testFailed name='test_can_set_allowed_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='4314' flowId='19088']
##teamcity[testFailed name='test_allows_valid_transition_processing_to_shipped' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='4489' flowId='19088']
##teamcity[testFailed name='test_can_set_approval_required_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='4659' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' flowId='19088']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest' flowId='19088']
##teamcity[testStarted name='test_denies_insufficient_inventory' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_insufficient_inventory' flowId='19088']
##teamcity[testFailed name='test_denies_insufficient_inventory' message='Failed asserting that an array contains |'Insufficient inventory|'.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:125|n' duration='2' flowId='19088']
##teamcity[testFinished name='test_denies_insufficient_inventory' duration='7' flowId='19088']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest' flowId='19088']
##teamcity[testSuiteFinished name='CLI Arguments' flowId='19088']
