##teamcity[testCount count='10' flowId='5092']
##teamcity[testSuiteStarted name='Tests\Feature\Products\CleanArchitectureTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest' flowId='5092']
##teamcity[testStarted name='it_can_get_featured_products' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_get_featured_products' flowId='5092']
##teamcity[testFinished name='it_can_get_featured_products' duration='21' flowId='5092']
##teamcity[testStarted name='it_can_handle_value_objects' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_handle_value_objects' flowId='5092']
##teamcity[testFailed name='it_can_handle_value_objects' message='Error: Call to undefined method App\Domain\Products\Entities\Product::getPriceAsMoney()' details='C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:172|n' duration='8' flowId='5092']
##teamcity[testFinished name='it_can_handle_value_objects' duration='11' flowId='5092']
##teamcity[testStarted name='it_throws_exception_for_insufficient_stock' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_throws_exception_for_insufficient_stock' flowId='5092']
##teamcity[testFailed name='it_throws_exception_for_insufficient_stock' message='Failed asserting that exception of type "Error" matches expected exception "InvalidArgumentException". Message was: "Cannot access private property App\Domain\Products\Entities\Product::$id" at|nC:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:207|n.' details='' duration='2' flowId='5092']
##teamcity[testFinished name='it_throws_exception_for_insufficient_stock' duration='5' flowId='5092']
##teamcity[testStarted name='it_can_create_product_using_clean_architecture' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_create_product_using_clean_architecture' flowId='5092']
##teamcity[testFinished name='it_can_create_product_using_clean_architecture' duration='4' flowId='5092']
##teamcity[testStarted name='it_can_check_stock_availability' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_check_stock_availability' flowId='5092']
##teamcity[testFailed name='it_can_check_stock_availability' message='Error: Cannot access private property App\Domain\Products\Entities\Product::$id' details='C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:106|n' duration='1' flowId='5092']
##teamcity[testFinished name='it_can_check_stock_availability' duration='3' flowId='5092']
##teamcity[testStarted name='it_can_manage_stock_using_clean_architecture' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_manage_stock_using_clean_architecture' flowId='5092']
##teamcity[testFailed name='it_can_manage_stock_using_clean_architecture' message='Error: Cannot access private property App\Domain\Products\Entities\Product::$id' details='C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:81|n' duration='1' flowId='5092']
##teamcity[testFinished name='it_can_manage_stock_using_clean_architecture' duration='4' flowId='5092']
##teamcity[testStarted name='it_can_update_product_using_clean_architecture' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_update_product_using_clean_architecture' flowId='5092']
##teamcity[testFailed name='it_can_update_product_using_clean_architecture' message='Error: Cannot access private property App\Domain\Products\Entities\Product::$id' details='C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:66|n' duration='1' flowId='5092']
##teamcity[testFinished name='it_can_update_product_using_clean_architecture' duration='5' flowId='5092']
##teamcity[testStarted name='it_can_handle_business_logic' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_handle_business_logic' flowId='5092']
##teamcity[testFailed name='it_can_handle_business_logic' message='Failed asserting that false is true.' details='C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:193|n' duration='4' flowId='5092']
##teamcity[testFinished name='it_can_handle_business_logic' duration='9' flowId='5092']
##teamcity[testStarted name='it_can_search_products' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_search_products' flowId='5092']
##teamcity[testFinished name='it_can_search_products' duration='9' flowId='5092']
##teamcity[testStarted name='it_can_calculate_product_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php::\Tests\Feature\Products\CleanArchitectureTest::it_can_calculate_product_price' flowId='5092']
##teamcity[testFailed name='it_can_calculate_product_price' message='Error: Cannot access private property App\Domain\Products\Entities\Product::$id' details='C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:122|n' duration='3' flowId='5092']
##teamcity[testFinished name='it_can_calculate_product_price' duration='8' flowId='5092']
##teamcity[testSuiteFinished name='Tests\Feature\Products\CleanArchitectureTest' flowId='5092']
