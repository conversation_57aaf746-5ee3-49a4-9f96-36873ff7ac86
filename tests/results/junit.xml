<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="CLI Arguments" tests="90" assertions="146" errors="29" failures="1" skipped="0" time="9.117141">
    <testsuite name="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" tests="14" assertions="33" errors="0" failures="0" skipped="0" time="2.329339">
      <testcase name="repository_can_delete_order" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="175" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="3" time="0.449462"/>
      <testcase name="it_can_resolve_order_repository_from_container" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="51" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="2" time="0.132801"/>
      <testcase name="repository_can_check_existence" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="143" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="4" time="0.191535"/>
      <testcase name="status_service_can_update_order_status" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="189" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="2" time="0.169452"/>
      <testcase name="repository_can_count_orders" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="156" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="3" time="0.136928"/>
      <testcase name="status_service_can_get_status_statistics" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="211" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="5" time="0.138751"/>
      <testcase name="notification_service_can_send_status_change_notification" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="268" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="0" time="0.133688"/>
      <testcase name="notification_service_can_send_order_created_notification" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="256" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="0" time="0.136429"/>
      <testcase name="it_can_resolve_order_services_from_container" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="64" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="2" time="0.137909"/>
      <testcase name="repository_can_find_orders_by_user_id" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="104" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="1" time="0.143077"/>
      <testcase name="repository_can_find_order_by_order_number" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="92" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="2" time="0.137442"/>
      <testcase name="status_service_can_perform_bulk_status_update" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="235" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="2" time="0.145650"/>
      <testcase name="repository_can_find_orders_by_status" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="123" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="1" time="0.141058"/>
      <testcase name="repository_can_save_and_retrieve_order" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\Orders\OrderInfrastructureTest.php" line="71" class="Tests\Unit\Infrastructure\Orders\OrderInfrastructureTest" classname="Tests.Unit.Infrastructure.Orders.OrderInfrastructureTest" assertions="6" time="0.135156"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" tests="13" assertions="17" errors="0" failures="0" skipped="0" time="1.882223">
      <testcase name="it_calculates_bulk_price_with_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="105" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.153279"/>
      <testcase name="it_calculates_current_price_without_sale" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="32" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="2" time="0.134996"/>
      <testcase name="it_finds_lowest_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="162" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.142890"/>
      <testcase name="it_compares_prices_correctly" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="149" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="3" time="0.137886"/>
      <testcase name="it_calculates_tax_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="135" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.145058"/>
      <testcase name="it_checks_if_price_is_in_range" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="213" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="2" time="0.151196"/>
      <testcase name="it_finds_highest_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="179" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.163484"/>
      <testcase name="it_calculates_discount_percentage" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="75" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.155072"/>
      <testcase name="it_calculates_discount_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="90" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.148047"/>
      <testcase name="it_calculates_variant_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="61" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.143071"/>
      <testcase name="it_calculates_price_with_tax" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="121" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.134878"/>
      <testcase name="it_calculates_average_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="196" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.133806"/>
      <testcase name="it_calculates_current_price_with_active_sale" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="46" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.138561"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php" tests="2" assertions="2" errors="1" failures="0" skipped="0" time="0.033441">
      <testcase name="test_cannot_create_payment_for_existing_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php" line="85" class="Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest" classname="Tests.Unit.Domain.Payment.Services.PaymentDomainServiceTest" assertions="2" time="0.020203"/>
      <testcase name="test_can_create_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php" line="45" class="Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest" classname="Tests.Unit.Domain.Payment.Services.PaymentDomainServiceTest" assertions="0" time="0.013238">
        <error type="Illuminate\Contracts\Container\BindingResolutionException">Tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest::test_can_create_payment&#13;
Illuminate\Contracts\Container\BindingResolutionException: Target class [config] does not exist.
&#13;
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:1019
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:890
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Foundation\Application.php:1077
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:821
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Foundation\Application.php:1057
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Container\Container.php:1653
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\DatabaseManager.php:388
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\DatabaseManager.php:93
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1918
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1884
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1664
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1583
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1619
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:1572
C:\laragon\www\modularecommerce\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php:2449
C:\laragon\www\modularecommerce\app\Domain\Payment\Entities\Payment.php:95
C:\laragon\www\modularecommerce\app\Domain\Payment\Entities\Payment.php:112
C:\laragon\www\modularecommerce\app\Domain\Payment\Services\PaymentDomainService.php:70
C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\Services\PaymentDomainServiceTest.php:72</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\DatabaseTest" file="C:\laragon\www\modularecommerce\tests\Unit\DatabaseTest.php" tests="2" assertions="5" errors="0" failures="0" skipped="0" time="0.289711">
      <testcase name="test_database_with_migrations" file="C:\laragon\www\modularecommerce\tests\Unit\DatabaseTest.php" line="15" class="Tests\Unit\DatabaseTest" classname="Tests.Unit.DatabaseTest" assertions="2" time="0.144706"/>
      <testcase name="test_user_creation" file="C:\laragon\www\modularecommerce\tests\Unit\DatabaseTest.php" line="27" class="Tests\Unit\DatabaseTest" classname="Tests.Unit.DatabaseTest" assertions="3" time="0.145005"/>
    </testsuite>
    <testsuite name="Tests\Unit\Infrastructure\ProductRepositoryTest" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" tests="10" assertions="28" errors="0" failures="0" skipped="0" time="1.413238">
      <testcase name="it_can_find_product_by_id" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="68" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="3" time="0.147359"/>
      <testcase name="it_can_find_product_by_sku" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="139" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="2" time="0.137031"/>
      <testcase name="it_can_count_products" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="273" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="1" time="0.134720"/>
      <testcase name="it_can_check_if_product_exists" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="314" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="2" time="0.147225"/>
      <testcase name="it_can_find_products_by_category" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="171" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="2" time="0.144706"/>
      <testcase name="it_returns_null_when_product_not_found" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="99" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="1" time="0.144657"/>
      <testcase name="it_can_save_a_product" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="29" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="7" time="0.142157"/>
      <testcase name="it_can_find_product_by_slug" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="109" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="2" time="0.139566"/>
      <testcase name="it_can_find_active_products" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="231" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="2" time="0.136775"/>
      <testcase name="it_can_get_statistics" file="C:\laragon\www\modularecommerce\tests\Unit\Infrastructure\ProductRepositoryTest.php" line="341" class="Tests\Unit\Infrastructure\ProductRepositoryTest" classname="Tests.Unit.Infrastructure.ProductRepositoryTest" assertions="6" time="0.139043"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" tests="16" assertions="50" errors="0" failures="0" skipped="0" time="2.315578">
      <testcase name="test_can_create_amount_only_refund" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="48" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="4" time="0.141214"/>
      <testcase name="test_can_create_full_refund" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="12" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="5" time="0.135466"/>
      <testcase name="test_invalid_percentage_throws_exception" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="117" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="1" time="0.134094"/>
      <testcase name="test_can_calculate_net_refund_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="82" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="1" time="0.142209"/>
      <testcase name="test_can_calculate_remaining_amounts" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="71" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="3" time="0.130386"/>
      <testcase name="test_refund_amount_exceeding_original_throws_exception" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="125" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="1" time="0.130748"/>
      <testcase name="test_can_create_refund_by_percentage" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="60" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="4" time="0.138096"/>
      <testcase name="test_can_create_fee_only_refund" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="37" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="4" time="0.139365"/>
      <testcase name="test_can_check_if_zero" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="143" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="1" time="0.146712"/>
      <testcase name="test_can_convert_to_array" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="173" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="9" time="0.138472"/>
      <testcase name="test_currency_mismatch_throws_exception" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="134" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="1" time="0.137840"/>
      <testcase name="test_can_create_partial_refund" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="24" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="5" time="0.160378"/>
      <testcase name="test_can_convert_to_string" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="190" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="3" time="0.154100"/>
      <testcase name="test_can_get_type_display_name" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="156" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="4" time="0.177024"/>
      <testcase name="test_can_increase_refund_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="91" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="2" time="0.157033"/>
      <testcase name="test_can_increase_refund_fee" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest.php" line="104" class="Tests\Unit\Domain\Payment\ValueObjects\RefundAmountTest" classname="Tests.Unit.Domain.Payment.ValueObjects.RefundAmountTest" assertions="2" time="0.152440"/>
    </testsuite>
    <testsuite name="Tests\Unit\CQRS\CommandBusTest" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" tests="4" assertions="7" errors="0" failures="0" skipped="0" time="0.700450">
      <testcase name="it_can_dispatch_command" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="50" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="1" time="0.189203"/>
      <testcase name="it_can_add_middleware" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="85" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="2" time="0.148664"/>
      <testcase name="it_throws_exception_for_unregistered_command" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="71" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="2" time="0.198997"/>
      <testcase name="it_can_register_command_handler" file="C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php" line="34" class="Tests\Unit\CQRS\CommandBusTest" classname="Tests.Unit.CQRS.CommandBusTest" assertions="2" time="0.163586"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" tests="28" assertions="0" errors="28" failures="0" skipped="0" time="0.000000">
      <testcase name="test_denies_cancellation_from_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="147" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_cancellation_from_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="329" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_cancellation_from_shipped" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="132" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_cancellation_from_shipped&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_cancellation_from_confirmed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="118" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_cancellation_from_confirmed&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_shipping_without_address" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="197" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_shipping_without_address&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_requires_approval_for_specific_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="297" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_requires_approval_for_specific_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_transition_with_approval" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="313" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_transition_with_approval&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_processing_without_stock" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="176" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_processing_without_stock&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_can_set_restricted_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="363" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_can_set_restricted_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_cancellation_from_pending" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="104" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_cancellation_from_pending&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_shipped_to_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_shipped_to_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_refund_from_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="212" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_refund_from_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_transition_for_non_order_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="283" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_transition_for_non_order_entity&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_delivered_to_completed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="90" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_delivered_to_completed&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_refund_after_period_expired" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="241" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_refund_after_period_expired&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_standard_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="395" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_standard_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_strict_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="404" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_strict_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_refund_from_non_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="227" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_refund_from_non_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_confirmation_without_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="161" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_confirmation_without_payment&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_confirmed_to_processing" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="46" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_confirmed_to_processing&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_returns_next_allowed_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="413" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_returns_next_allowed_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_transition_without_target_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="271" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_transition_without_target_status&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_pending_to_confirmed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="30" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_pending_to_confirmed&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_same_status_transition" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="256" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_same_status_transition&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_is_applicable_with_target_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="336" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_is_applicable_with_target_status&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_can_set_allowed_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="343" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_can_set_allowed_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_processing_to_shipped" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="61" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_processing_to_shipped&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_can_set_approval_required_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="379" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_can_set_approval_required_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" tests="1" assertions="4" errors="0" failures="1" skipped="0" time="0.153160">
      <testcase name="test_denies_insufficient_inventory" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="115" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="4" time="0.153160">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_insufficient_inventory&#13;
Failed asserting that an array contains 'Insufficient inventory'.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:125</failure>
      </testcase>
    </testsuite>
  </testsuite>
</testsuites>
