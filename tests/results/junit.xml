<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Feature\Products\CleanArchitectureTest" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" tests="10" assertions="14" errors="5" failures="2" skipped="0" time="1.876624">
    <testcase name="it_can_get_featured_products" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="151" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="2" time="0.496495"/>
    <testcase name="it_can_handle_value_objects" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="166" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="0" time="0.146468">
      <error type="Error">Tests\Feature\Products\CleanArchitectureTest::it_can_handle_value_objects&#13;
Error: Call to undefined method App\Domain\Products\Entities\Product::getPriceAsMoney()
&#13;
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:172</error>
    </testcase>
    <testcase name="it_throws_exception_for_insufficient_stock" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="198" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="1" time="0.150406">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Feature\Products\CleanArchitectureTest::it_throws_exception_for_insufficient_stock&#13;
Failed asserting that exception of type "Error" matches expected exception "InvalidArgumentException". Message was: "Cannot access private property App\Domain\Products\Entities\Product::$id" at
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:207
.</failure>
    </testcase>
    <testcase name="it_can_create_product_using_clean_architecture" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="31" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="7" time="0.138193"/>
    <testcase name="it_can_check_stock_availability" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="100" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="0" time="0.139305">
      <error type="Error">Tests\Feature\Products\CleanArchitectureTest::it_can_check_stock_availability&#13;
Error: Cannot access private property App\Domain\Products\Entities\Product::$id
&#13;
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:106</error>
    </testcase>
    <testcase name="it_can_manage_stock_using_clean_architecture" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="75" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="0" time="0.138793">
      <error type="Error">Tests\Feature\Products\CleanArchitectureTest::it_can_manage_stock_using_clean_architecture&#13;
Error: Cannot access private property App\Domain\Products\Entities\Product::$id
&#13;
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:81</error>
    </testcase>
    <testcase name="it_can_update_product_using_clean_architecture" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="56" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="0" time="0.143620">
      <error type="Error">Tests\Feature\Products\CleanArchitectureTest::it_can_update_product_using_clean_architecture&#13;
Error: Cannot access private property App\Domain\Products\Entities\Product::$id
&#13;
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:66</error>
    </testcase>
    <testcase name="it_can_handle_business_logic" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="181" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="2" time="0.195687">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Feature\Products\CleanArchitectureTest::it_can_handle_business_logic&#13;
Failed asserting that false is true.
&#13;
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:193</failure>
    </testcase>
    <testcase name="it_can_search_products" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="132" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="2" time="0.160173"/>
    <testcase name="it_can_calculate_product_price" file="C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php" line="116" class="Tests\Feature\Products\CleanArchitectureTest" classname="Tests.Feature.Products.CleanArchitectureTest" assertions="0" time="0.167486">
      <error type="Error">Tests\Feature\Products\CleanArchitectureTest::it_can_calculate_product_price&#13;
Error: Cannot access private property App\Domain\Products\Entities\Product::$id
&#13;
C:\laragon\www\modularecommerce\tests\Feature\Products\CleanArchitectureTest.php:122</error>
    </testcase>
  </testsuite>
</testsuites>
