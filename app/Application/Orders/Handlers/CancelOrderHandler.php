<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\CancelOrderCommand;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CancelOrderHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(CancelOrderCommand $command): OrderDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Siparişi bul
                $order = $this->orderRepository->findById($command->getOrderId());
                
                if (!$order) {
                    throw new OrderNotFoundException("Order with ID {$command->getOrderId()} not found");
                }

                $oldStatus = $order->getStatus();

                // Siparişi iptal et
                $order->cancel($command->getReason());

                // Siparişi kaydet
                $savedOrder = $this->orderRepository->save($order);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedOrder);

                Log::info('Order cancelled successfully', [
                    'order_id' => $savedOrder->getId(),
                    'order_number' => $savedOrder->getOrderNumber()->getValue(),
                    'old_status' => is_object($oldStatus) ? $oldStatus->value : $oldStatus,
                    'user_id' => $command->getUserId(),
                    'reason' => $command->getReason()
                ]);

                return OrderDTO::fromEntity($savedOrder);

            } catch (\Exception $e) {
                Log::error('Failed to cancel order', [
                    'order_id' => $command->getOrderId(),
                    'user_id' => $command->getUserId(),
                    'reason' => $command->getReason(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }
}
