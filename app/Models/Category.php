<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;

class Category extends Model
{
    use HasFactory, SoftDeletes, Searchable;

    protected $fillable = [
        'name',
        'description',
        'parent_id',
        'status',
        'slug',
        'position',
        'image',
        'icon',
        'featured',
        'show_in_menu',
    ];

    protected $casts = [
        'status' => 'boolean',
        'featured' => 'boolean',
        'show_in_menu' => 'boolean',
        'position' => 'integer',
    ];

    /**
     * Get the category ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Check if category is active
     */
    public function isActive(): bool
    {
        return $this->status === true;
    }

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * Get the child categories
     */
    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id')
                    ->orderBy('position', 'asc');
    }

    /**
     * Get all descendants
     */
    public function descendants()
    {
        $descendants = collect();

        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->descendants());
        }

        return $descendants;
    }

    /**
     * Get all ancestors
     */
    public function ancestors()
    {
        $ancestors = collect();
        $parent = $this->parent;

        while ($parent) {
            $ancestors->push($parent);
            $parent = $parent->parent;
        }

        return $ancestors;
    }

    /**
     * Get the full path of the category
     */
    public function getPathAttribute()
    {
        $path = collect([$this]);
        $parent = $this->parent;

        while ($parent) {
            $path->prepend($parent);
            $parent = $parent->parent;
        }

        return $path;
    }

    /**
     * Get the breadcrumb of the category
     */
    public function getBreadcrumbAttribute()
    {
        return $this->path->pluck('name')->implode(' > ');
    }

    /**
     * Get the SEO friendly URL for the category
     */
    public function getSeoUrlAttribute()
    {
        // Slug yoksa, adı kullanarak slug oluştur
        $slug = $this->slug ?: \Illuminate\Support\Str::slug($this->name);

        // Kategori ID'sini ekle
        return $slug . '-c-' . $this->id;
    }

    /**
     * Get the URL for the category
     */
    public function getUrlAttribute()
    {
        return route('frontend.products.show.seo', $this->seo_url);
    }

    /**
     * Get the products for the category
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the attributes for the category
     */
    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'category_attributes')
                    ->withPivot('is_required', 'is_filterable', 'position')
                    ->withTimestamps();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'parent_id' => $this->parent_id,
            'status' => $this->status,
            'slug' => $this->slug,
            'position' => $this->position,
        ];
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        return $this->status === 1 || $this->status === true;
    }
}
