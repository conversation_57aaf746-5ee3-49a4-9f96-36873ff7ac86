<?php

namespace App\Domain\Notifications\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * RecipientInfo Value Object
 * Bildirim alıcısı bilgileri için immutable value object
 */
class RecipientInfo extends ValueObject
{
    public const TYPE_USER = 'user';
    public const TYPE_ADMIN = 'admin';
    public const TYPE_SYSTEM = 'system';
    public const TYPE_GUEST = 'guest';
    public const TYPE_GROUP = 'group';
    public const TYPE_EXTERNAL = 'external';

    private string $type;
    private ?int $userId;
    private ?string $email;
    private ?string $phone;
    private ?string $name;
    private ?string $externalId;
    private array $preferences;
    private array $metadata;

    private function __construct(
        string $type,
        ?int $userId = null,
        ?string $email = null,
        ?string $phone = null,
        ?string $name = null,
        ?string $externalId = null,
        array $preferences = [],
        array $metadata = []
    ) {
        $this->validateRecipient($type, $userId, $email, $phone);
        
        $this->type = $type;
        $this->userId = $userId;
        $this->email = $email ? trim($email) : null;
        $this->phone = $phone ? trim($phone) : null;
        $this->name = $name ? trim($name) : null;
        $this->externalId = $externalId ? trim($externalId) : null;
        $this->preferences = $preferences;
        $this->metadata = $metadata;
    }

    /**
     * Kullanıcı alıcısı oluştur
     */
    public static function user(
        int $userId,
        ?string $email = null,
        ?string $phone = null,
        ?string $name = null,
        array $preferences = [],
        array $metadata = []
    ): self {
        return new self(
            self::TYPE_USER,
            $userId,
            $email,
            $phone,
            $name,
            null,
            $preferences,
            $metadata
        );
    }

    /**
     * Admin alıcısı oluştur
     */
    public static function admin(
        int $adminId,
        ?string $email = null,
        ?string $phone = null,
        ?string $name = null,
        array $preferences = [],
        array $metadata = []
    ): self {
        return new self(
            self::TYPE_ADMIN,
            $adminId,
            $email,
            $phone,
            $name,
            null,
            $preferences,
            $metadata
        );
    }

    /**
     * Sistem alıcısı oluştur
     */
    public static function system(
        string $email,
        ?string $name = null,
        array $metadata = []
    ): self {
        return new self(
            self::TYPE_SYSTEM,
            null,
            $email,
            null,
            $name ?: 'System',
            null,
            [],
            $metadata
        );
    }

    /**
     * Misafir alıcısı oluştur
     */
    public static function guest(
        string $email,
        ?string $phone = null,
        ?string $name = null,
        array $metadata = []
    ): self {
        return new self(
            self::TYPE_GUEST,
            null,
            $email,
            $phone,
            $name,
            null,
            [],
            $metadata
        );
    }

    /**
     * Grup alıcısı oluştur
     */
    public static function group(
        string $groupId,
        ?string $name = null,
        array $metadata = []
    ): self {
        return new self(
            self::TYPE_GROUP,
            null,
            null,
            null,
            $name,
            $groupId,
            [],
            $metadata
        );
    }

    /**
     * Harici alıcı oluştur
     */
    public static function external(
        string $externalId,
        ?string $email = null,
        ?string $phone = null,
        ?string $name = null,
        array $metadata = []
    ): self {
        return new self(
            self::TYPE_EXTERNAL,
            null,
            $email,
            $phone,
            $name,
            $externalId,
            [],
            $metadata
        );
    }

    /**
     * E-posta alıcısı oluştur
     */
    public static function email(string $email, ?string $name = null): self
    {
        return new self(
            self::TYPE_EXTERNAL,
            null,
            $email,
            null,
            $name,
            null,
            [],
            []
        );
    }

    /**
     * Telefon alıcısı oluştur
     */
    public static function phone(string $phone, ?string $name = null): self
    {
        return new self(
            self::TYPE_EXTERNAL,
            null,
            null,
            $phone,
            $name,
            null,
            [],
            []
        );
    }

    /**
     * Array'den RecipientInfo oluştur
     */
    public static function fromArray(array $data): self
    {
        return new self(
            type: $data['type'] ?? self::TYPE_EXTERNAL,
            userId: $data['user_id'] ?? null,
            email: $data['email'] ?? null,
            phone: $data['phone'] ?? null,
            name: $data['name'] ?? null,
            externalId: $data['external_id'] ?? null,
            preferences: $data['preferences'] ?? [],
            metadata: $data['metadata'] ?? []
        );
    }

    /**
     * Alıcı tipini getir
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Kullanıcı ID'sini getir
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * E-posta adresini getir
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * Telefon numarasını getir
     */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    /**
     * Adını getir
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Harici ID'yi getir
     */
    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    /**
     * Tercihleri getir
     */
    public function getPreferences(): array
    {
        return $this->preferences;
    }

    /**
     * Metadata'yı getir
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Belirli tercih değerini getir
     */
    public function getPreference(string $key, $default = null)
    {
        return $this->preferences[$key] ?? $default;
    }

    /**
     * Belirli metadata değerini getir
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Görüntüleme adını getir
     */
    public function getDisplayName(): string
    {
        if ($this->name) {
            return $this->name;
        }

        if ($this->email) {
            return $this->email;
        }

        if ($this->phone) {
            return $this->phone;
        }

        if ($this->externalId) {
            return $this->externalId;
        }

        return 'Bilinmeyen Alıcı';
    }

    /**
     * Tip adını getir
     */
    public function getTypeName(): string
    {
        return match ($this->type) {
            self::TYPE_USER => 'Kullanıcı',
            self::TYPE_ADMIN => 'Yönetici',
            self::TYPE_SYSTEM => 'Sistem',
            self::TYPE_GUEST => 'Misafir',
            self::TYPE_GROUP => 'Grup',
            self::TYPE_EXTERNAL => 'Harici',
            default => 'Bilinmeyen'
        };
    }

    /**
     * Kullanıcı alıcısı mı kontrol et
     */
    public function isUser(): bool
    {
        return $this->type === self::TYPE_USER;
    }

    /**
     * Admin alıcısı mı kontrol et
     */
    public function isAdmin(): bool
    {
        return $this->type === self::TYPE_ADMIN;
    }

    /**
     * Sistem alıcısı mı kontrol et
     */
    public function isSystem(): bool
    {
        return $this->type === self::TYPE_SYSTEM;
    }

    /**
     * Misafir alıcısı mı kontrol et
     */
    public function isGuest(): bool
    {
        return $this->type === self::TYPE_GUEST;
    }

    /**
     * Grup alıcısı mı kontrol et
     */
    public function isGroup(): bool
    {
        return $this->type === self::TYPE_GROUP;
    }

    /**
     * Harici alıcı mı kontrol et
     */
    public function isExternal(): bool
    {
        return $this->type === self::TYPE_EXTERNAL;
    }

    /**
     * E-posta adresi var mı kontrol et
     */
    public function hasEmail(): bool
    {
        return !empty($this->email);
    }

    /**
     * Telefon numarası var mı kontrol et
     */
    public function hasPhone(): bool
    {
        return !empty($this->phone);
    }

    /**
     * Kayıtlı kullanıcı mı kontrol et
     */
    public function isRegisteredUser(): bool
    {
        return $this->userId !== null;
    }

    /**
     * Belirli kanal için uygun mu kontrol et
     */
    public function isSuitableForChannel(NotificationChannel $channel): bool
    {
        return match ($channel->getValue()) {
            NotificationChannel::EMAIL => $this->hasEmail(),
            NotificationChannel::SMS, NotificationChannel::WHATSAPP => $this->hasPhone(),
            NotificationChannel::PUSH, NotificationChannel::IN_APP => $this->isRegisteredUser(),
            NotificationChannel::WEBHOOK, NotificationChannel::SLACK, NotificationChannel::DISCORD, NotificationChannel::TELEGRAM => true,
            default => false
        };
    }

    /**
     * Bildirim tercihini kontrol et
     */
    public function allowsNotificationType(string $notificationType): bool
    {
        $preferences = $this->getPreference('notification_types', []);
        
        // Eğer tercih belirtilmemişse, varsayılan olarak izin ver
        if (empty($preferences)) {
            return true;
        }

        return in_array($notificationType, $preferences);
    }

    /**
     * Kanal tercihini kontrol et
     */
    public function allowsChannel(string $channel): bool
    {
        $preferences = $this->getPreference('channels', []);
        
        // Eğer tercih belirtilmemişse, varsayılan olarak izin ver
        if (empty($preferences)) {
            return true;
        }

        return in_array($channel, $preferences);
    }

    /**
     * Sessiz saatlerde mi kontrol et
     */
    public function isInQuietHours(): bool
    {
        $quietHours = $this->getPreference('quiet_hours');
        
        if (!$quietHours || !isset($quietHours['start']) || !isset($quietHours['end'])) {
            return false;
        }

        $now = new \DateTime();
        $currentTime = $now->format('H:i');
        
        return $currentTime >= $quietHours['start'] && $currentTime <= $quietHours['end'];
    }

    /**
     * Zaman dilimini getir
     */
    public function getTimezone(): string
    {
        return $this->getPreference('timezone', 'Europe/Istanbul');
    }

    /**
     * Dil tercihini getir
     */
    public function getLanguage(): string
    {
        return $this->getPreference('language', 'tr');
    }

    /**
     * Benzersiz tanımlayıcı getir
     */
    public function getUniqueIdentifier(): string
    {
        if ($this->userId) {
            return $this->type . ':' . $this->userId;
        }

        if ($this->externalId) {
            return $this->type . ':' . $this->externalId;
        }

        if ($this->email) {
            return $this->type . ':' . $this->email;
        }

        if ($this->phone) {
            return $this->type . ':' . $this->phone;
        }

        return $this->type . ':unknown';
    }

    /**
     * Değerleri validate et
     */
    protected function validateRecipient(string $type, ?int $userId, ?string $email, ?string $phone): void
    {
        $validTypes = [
            self::TYPE_USER,
            self::TYPE_ADMIN,
            self::TYPE_SYSTEM,
            self::TYPE_GUEST,
            self::TYPE_GROUP,
            self::TYPE_EXTERNAL,
        ];

        if (!in_array($type, $validTypes)) {
            throw new \InvalidArgumentException("Invalid recipient type: {$type}");
        }

        // En az bir iletişim bilgisi gerekli
        if (!$userId && !$email && !$phone) {
            throw new \InvalidArgumentException('At least one contact information (userId, email, or phone) is required');
        }

        // E-posta format kontrolü
        if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email format');
        }

        // Telefon format kontrolü
        if ($phone && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $phone)) {
            throw new \InvalidArgumentException('Invalid phone number format');
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && 
               $this->getUniqueIdentifier() === $other->getUniqueIdentifier();
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'type' => $this->type,
            'type_name' => $this->getTypeName(),
            'user_id' => $this->userId,
            'email' => $this->email,
            'phone' => $this->phone,
            'name' => $this->name,
            'external_id' => $this->externalId,
            'display_name' => $this->getDisplayName(),
            'unique_identifier' => $this->getUniqueIdentifier(),
            'is_user' => $this->isUser(),
            'is_admin' => $this->isAdmin(),
            'is_system' => $this->isSystem(),
            'is_guest' => $this->isGuest(),
            'is_group' => $this->isGroup(),
            'is_external' => $this->isExternal(),
            'has_email' => $this->hasEmail(),
            'has_phone' => $this->hasPhone(),
            'is_registered_user' => $this->isRegisteredUser(),
            'is_in_quiet_hours' => $this->isInQuietHours(),
            'timezone' => $this->getTimezone(),
            'language' => $this->getLanguage(),
            'preferences' => $this->preferences,
            'metadata' => $this->metadata,
        ];
    }
}
