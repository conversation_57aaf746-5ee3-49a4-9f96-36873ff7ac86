<?php

namespace App\Domain\Payment\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;
use App\Core\Domain\ValueObjects\Money;

/**
 * RefundAmount Value Object
 * İade tutarı için immutable value object
 */
class RefundAmount extends ValueObject
{
    // Refund Types
    public const TYPE_FULL = 'full';
    public const TYPE_PARTIAL = 'partial';
    public const TYPE_FEE_ONLY = 'fee_only';
    public const TYPE_AMOUNT_ONLY = 'amount_only';

    private Money $refundAmount;
    private Money $refundFee;
    private Money $netRefundAmount;
    private Money $originalAmount;
    private Money $originalFee;
    private string $type;
    private float $percentage;

    private function __construct(
        Money $refundAmount,
        Money $originalAmount,
        Money $refundFee = null,
        Money $originalFee = null
    ) {
        $this->refundAmount = $refundAmount;
        $this->originalAmount = $originalAmount;
        $this->originalFee = $originalFee ?? Money::zero($originalAmount->getCurrency());
        $this->refundFee = $refundFee ?? Money::zero($originalAmount->getCurrency());
        
        $this->netRefundAmount = $this->refundAmount->subtract($this->refundFee);
        $this->type = $this->calculateType();
        $this->percentage = $this->calculatePercentage();
        
        $this->validate();
    }

    /**
     * Tam iade oluştur
     */
    public static function createFull(PaymentAmount $originalPayment): self
    {
        return new self(
            $originalPayment->getAmount(),
            $originalPayment->getAmount(),
            $originalPayment->getFeeAmount(),
            $originalPayment->getFeeAmount()
        );
    }

    /**
     * Kısmi iade oluştur
     */
    public static function createPartial(
        Money $refundAmount,
        PaymentAmount $originalPayment,
        bool $refundFeeProportionally = true
    ): self {
        $originalFee = $originalPayment->getFeeAmount();
        $refundFee = Money::zero($refundAmount->getCurrency());

        if ($refundFeeProportionally && !$originalFee->isZero()) {
            $percentage = $refundAmount->getAmount() / $originalPayment->getAmount()->getAmount();
            $refundFee = $originalFee->multiply($percentage);
        }

        return new self(
            $refundAmount,
            $originalPayment->getAmount(),
            $refundFee,
            $originalFee
        );
    }

    /**
     * Sadece komisyon iadesi oluştur
     */
    public static function createFeeOnly(PaymentAmount $originalPayment): self
    {
        return new self(
            Money::zero($originalPayment->getCurrency()),
            $originalPayment->getAmount(),
            $originalPayment->getFeeAmount(),
            $originalPayment->getFeeAmount()
        );
    }

    /**
     * Sadece tutar iadesi oluştur (komisyon hariç)
     */
    public static function createAmountOnly(
        Money $refundAmount,
        PaymentAmount $originalPayment
    ): self {
        return new self(
            $refundAmount,
            $originalPayment->getAmount(),
            Money::zero($refundAmount->getCurrency()),
            $originalPayment->getFeeAmount()
        );
    }

    /**
     * Yüzde ile iade oluştur
     */
    public static function createByPercentage(
        float $percentage,
        PaymentAmount $originalPayment,
        bool $refundFeeProportionally = true
    ): self {
        if ($percentage < 0 || $percentage > 100) {
            throw new \InvalidArgumentException('Percentage must be between 0 and 100');
        }

        $refundAmount = $originalPayment->getAmount()->multiply($percentage / 100);
        
        return self::createPartial($refundAmount, $originalPayment, $refundFeeProportionally);
    }

    /**
     * Mevcut değerlerden oluştur
     */
    public static function create(
        Money $refundAmount,
        Money $originalAmount,
        Money $refundFee = null,
        Money $originalFee = null
    ): self {
        return new self($refundAmount, $originalAmount, $refundFee, $originalFee);
    }

    /**
     * Tam iade oluştur (alias)
     */
    public static function fullRefund(PaymentAmount $originalPayment): self
    {
        return self::createFull($originalPayment);
    }

    /**
     * Kısmi iade oluştur (alias)
     */
    public static function partialRefund(
        Money $refundAmount,
        PaymentAmount $originalPayment,
        bool $refundFeeProportionally = true
    ): self {
        return self::createPartial($refundAmount, $originalPayment, $refundFeeProportionally);
    }

    /**
     * İade tutarını getir
     */
    public function getRefundAmount(): Money
    {
        return $this->refundAmount;
    }

    /**
     * İade komisyonunu getir
     */
    public function getRefundFee(): Money
    {
        return $this->refundFee;
    }

    /**
     * Net iade tutarını getir
     */
    public function getNetRefundAmount(): Money
    {
        return $this->netRefundAmount;
    }

    /**
     * Orijinal tutarı getir
     */
    public function getOriginalAmount(): Money
    {
        return $this->originalAmount;
    }

    /**
     * Orijinal komisyonu getir
     */
    public function getOriginalFee(): Money
    {
        return $this->originalFee;
    }

    /**
     * İade tipini getir
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * İade yüzdesini getir
     */
    public function getPercentage(): float
    {
        return $this->percentage;
    }

    /**
     * Para birimini getir
     */
    public function getCurrency(): string
    {
        return $this->refundAmount->getCurrency();
    }

    /**
     * Tam iade mi kontrol et
     */
    public function isFullRefund(): bool
    {
        return $this->type === self::TYPE_FULL;
    }

    /**
     * Kısmi iade mi kontrol et
     */
    public function isPartialRefund(): bool
    {
        return $this->type === self::TYPE_PARTIAL;
    }

    /**
     * Sadece komisyon iadesi mi kontrol et
     */
    public function isFeeOnlyRefund(): bool
    {
        return $this->type === self::TYPE_FEE_ONLY;
    }

    /**
     * Sadece tutar iadesi mi kontrol et
     */
    public function isAmountOnlyRefund(): bool
    {
        return $this->type === self::TYPE_AMOUNT_ONLY;
    }

    /**
     * İade tutarı sıfır mı kontrol et
     */
    public function isZero(): bool
    {
        return $this->refundAmount->isZero() && $this->refundFee->isZero();
    }

    /**
     * Geçerli iade tutarı mı kontrol et
     */
    public function isValid(): bool
    {
        // İade tutarı negatif olamaz
        if ($this->refundAmount->getAmount() < 0 || $this->refundFee->getAmount() < 0) {
            return false;
        }

        // İade tutarı orijinal tutardan fazla olamaz
        if ($this->refundAmount->getAmount() > $this->originalAmount->getAmount()) {
            return false;
        }

        // İade komisyonu orijinal komisyondan fazla olamaz
        if ($this->refundFee->getAmount() > $this->originalFee->getAmount()) {
            return false;
        }

        return true;
    }

    /**
     * Kalan tutarı hesapla
     */
    public function getRemainingAmount(): Money
    {
        return $this->originalAmount->subtract($this->refundAmount);
    }

    /**
     * Kalan komisyonu hesapla
     */
    public function getRemainingFee(): Money
    {
        return $this->originalFee->subtract($this->refundFee);
    }

    /**
     * Toplam kalan tutarı hesapla
     */
    public function getTotalRemainingAmount(): Money
    {
        return $this->getRemainingAmount()->add($this->getRemainingFee());
    }

    /**
     * İade tipini hesapla
     */
    private function calculateType(): string
    {
        $isFullAmount = $this->refundAmount->equals($this->originalAmount);
        $isFullFee = $this->refundFee->equals($this->originalFee);
        $isZeroAmount = $this->refundAmount->isZero();
        $isZeroFee = $this->refundFee->isZero();

        if ($isFullAmount && $isFullFee) {
            return self::TYPE_FULL;
        }

        if ($isZeroAmount && !$isZeroFee) {
            return self::TYPE_FEE_ONLY;
        }

        if (!$isZeroAmount && $isZeroFee) {
            return self::TYPE_AMOUNT_ONLY;
        }

        return self::TYPE_PARTIAL;
    }

    /**
     * İade yüzdesini hesapla
     */
    private function calculatePercentage(): float
    {
        if ($this->originalAmount->isZero()) {
            return 0.0;
        }

        return ($this->refundAmount->getAmount() / $this->originalAmount->getAmount()) * 100;
    }

    /**
     * İade tutarını artır
     */
    public function increaseRefundAmount(Money $additionalAmount): self
    {
        $newRefundAmount = $this->refundAmount->add($additionalAmount);
        
        return new self(
            $newRefundAmount,
            $this->originalAmount,
            $this->refundFee,
            $this->originalFee
        );
    }

    /**
     * İade komisyonunu artır
     */
    public function increaseRefundFee(Money $additionalFee): self
    {
        $newRefundFee = $this->refundFee->add($additionalFee);
        
        return new self(
            $this->refundAmount,
            $this->originalAmount,
            $newRefundFee,
            $this->originalFee
        );
    }

    /**
     * İade tipinin açıklamasını getir
     */
    public function getTypeDisplayName(): string
    {
        return match ($this->type) {
            self::TYPE_FULL => 'Tam İade',
            self::TYPE_PARTIAL => 'Kısmi İade',
            self::TYPE_FEE_ONLY => 'Sadece Komisyon İadesi',
            self::TYPE_AMOUNT_ONLY => 'Sadece Tutar İadesi',
            default => 'Bilinmeyen',
        };
    }

    /**
     * Validation
     */
    protected function validate(): void
    {
        if (!$this->isValid()) {
            throw new \InvalidArgumentException('Invalid refund amount configuration');
        }

        // Para birimleri aynı olmalı
        if ($this->refundAmount->getCurrency() !== $this->originalAmount->getCurrency()) {
            throw new \InvalidArgumentException('Currency mismatch between refund and original amounts');
        }
    }

    /**
     * String temsilini getir
     */
    public function __toString(): string
    {
        return sprintf(
            '%s (%s - %.1f%%)',
            $this->netRefundAmount->__toString(),
            $this->getTypeDisplayName(),
            $this->percentage
        );
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'refund_amount' => $this->refundAmount->getAmount(),
            'refund_fee' => $this->refundFee->getAmount(),
            'net_refund_amount' => $this->netRefundAmount->getAmount(),
            'original_amount' => $this->originalAmount->getAmount(),
            'original_fee' => $this->originalFee->getAmount(),
            'remaining_amount' => $this->getRemainingAmount()->getAmount(),
            'remaining_fee' => $this->getRemainingFee()->getAmount(),
            'total_remaining' => $this->getTotalRemainingAmount()->getAmount(),
            'currency' => $this->getCurrency(),
            'type' => $this->type,
            'type_display_name' => $this->getTypeDisplayName(),
            'percentage' => $this->percentage,
            'is_full_refund' => $this->isFullRefund(),
            'is_partial_refund' => $this->isPartialRefund(),
            'is_fee_only' => $this->isFeeOnlyRefund(),
            'is_amount_only' => $this->isAmountOnlyRefund(),
            'is_zero' => $this->isZero(),
        ];
    }
}
