<?php

namespace App\Domain\Shared\Rules\Shipping;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRule;
use App\Domain\Shared\Rules\BusinessRuleResult;
use App\Domain\Shared\ValueObjects\Money;

/**
 * ShippingCostRule
 * Kargo ücreti hesaplama kuralı
 */
class ShippingCostRule extends BusinessRule implements ShippingRuleInterface
{
    private array $costRules;
    private int $priority;

    public function __construct(
        array $costRules,
        int $priority = 100,
        string $name = 'shipping_cost',
        string $description = 'Shipping cost calculation rule',
        string $group = 'shipping'
    ) {
        parent::__construct($name, $description, $group);
        
        $this->costRules = $this->validateCostRules($costRules);
        $this->priority = $priority;
        
        $this->setMetadataValue('cost_rules', $this->costRules);
        $this->setMetadataValue('priority', $this->priority);
    }

    /**
     * Ağırlık bazlı kargo ücreti kuralı
     */
    public static function weightBased(array $weightTiers, int $priority = 100): self
    {
        return new self(
            [
                'type' => 'weight',
                'tiers' => $weightTiers,
                'base_cost' => Money::fromAmount(15, 'TRY'), // Temel kargo ücreti
            ],
            $priority,
            'weight_based_shipping',
            'Weight-based shipping cost calculation'
        );
    }

    /**
     * Tutar bazlı kargo ücreti kuralı
     */
    public static function amountBased(array $amountTiers, int $priority = 100): self
    {
        return new self(
            [
                'type' => 'amount',
                'tiers' => $amountTiers,
                'base_cost' => Money::fromAmount(15, 'TRY'),
            ],
            $priority,
            'amount_based_shipping',
            'Amount-based shipping cost calculation'
        );
    }

    /**
     * Sabit kargo ücreti kuralı
     */
    public static function fixed(Money $cost, int $priority = 100): self
    {
        return new self(
            [
                'type' => 'fixed',
                'cost' => $cost,
            ],
            $priority,
            'fixed_shipping',
            'Fixed shipping cost rule'
        );
    }

    /**
     * Ücretsiz kargo kuralı
     */
    public static function free(Money $minimumAmount = null, int $priority = 50): self
    {
        return new self(
            [
                'type' => 'free',
                'minimum_amount' => $minimumAmount,
            ],
            $priority,
            'free_shipping',
            'Free shipping rule'
        );
    }

    public function applyRule(EntityInterface $entity, array $context = []): ShippingRuleResult
    {
        $ruleType = $this->costRules['type'];

        switch ($ruleType) {
            case 'free':
                return $this->applyFreeShippingRule($context);
            
            case 'fixed':
                return $this->applyFixedCostRule($context);
            
            case 'weight':
                return $this->applyWeightBasedRule($context);
            
            case 'amount':
                return $this->applyAmountBasedRule($context);
            
            default:
                return ShippingRuleResult::restricted(
                    $this->name,
                    ['Unknown shipping rule type: ' . $ruleType]
                );
        }
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getRuleType(): string
    {
        return $this->costRules['type'] ?? 'unknown';
    }

    protected function checkApplicability(EntityInterface $entity, array $context = []): bool
    {
        return !empty($this->costRules['type']);
    }

    protected function performEvaluation(EntityInterface $entity, array $context = []): BusinessRuleResult
    {
        $result = $this->applyRule($entity, $context);
        
        return BusinessRuleResult::valid($this->name, [
            'shipping_result' => $result->toArray(),
        ]);
    }

    /**
     * Ücretsiz kargo kuralını uygula
     */
    private function applyFreeShippingRule(array $context): ShippingRuleResult
    {
        $minimumAmount = $this->costRules['minimum_amount'] ?? null;
        
        if ($minimumAmount) {
            $orderAmount = $this->extractOrderAmount($context);
            
            if (!$orderAmount || $orderAmount->isLessThan($minimumAmount)) {
                return ShippingRuleResult::restricted(
                    $this->name,
                    ["Minimum order amount required for free shipping: {$minimumAmount->getAmountInMajorUnit()}"],
                    'Order amount below free shipping threshold'
                );
            }
        }

        return ShippingRuleResult::freeShipping(
            $this->name,
            3, // 3 gün tahmini teslimat
            ['standard', 'express'],
            'Free shipping applied'
        );
    }

    /**
     * Sabit ücret kuralını uygula
     */
    private function applyFixedCostRule(array $context): ShippingRuleResult
    {
        $cost = $this->costRules['cost'];

        return ShippingRuleResult::allowed(
            $this->name,
            $cost,
            3,
            ['standard'],
            "Fixed shipping cost: {$cost->getAmountInMajorUnit()}"
        );
    }

    /**
     * Ağırlık bazlı kural uygula
     */
    private function applyWeightBasedRule(array $context): ShippingRuleResult
    {
        $weight = $this->extractWeight($context);
        
        if ($weight <= 0) {
            return ShippingRuleResult::restricted(
                $this->name,
                ['Weight information required for shipping calculation']
            );
        }

        $tier = $this->findApplicableWeightTier($weight);
        
        if (!$tier) {
            return ShippingRuleResult::restricted(
                $this->name,
                ['No shipping available for this weight: ' . $weight . 'kg']
            );
        }

        $cost = $tier['cost'] ?? $this->costRules['base_cost'];
        $estimatedDays = $tier['estimated_days'] ?? 3;

        return ShippingRuleResult::allowed(
            $this->name,
            $cost,
            $estimatedDays,
            ['standard'],
            "Weight-based shipping: {$weight}kg"
        );
    }

    /**
     * Tutar bazlı kural uygula
     */
    private function applyAmountBasedRule(array $context): ShippingRuleResult
    {
        $orderAmount = $this->extractOrderAmount($context);
        
        if (!$orderAmount) {
            return ShippingRuleResult::restricted(
                $this->name,
                ['Order amount required for shipping calculation']
            );
        }

        $tier = $this->findApplicableAmountTier($orderAmount);
        
        if (!$tier) {
            $cost = $this->costRules['base_cost'];
        } else {
            $cost = $tier['cost'] ?? $this->costRules['base_cost'];
        }

        $estimatedDays = $tier['estimated_days'] ?? 3;

        return ShippingRuleResult::allowed(
            $this->name,
            $cost,
            $estimatedDays,
            ['standard'],
            "Amount-based shipping: {$orderAmount->getAmountInMajorUnit()}"
        );
    }

    /**
     * Context'ten sipariş tutarını çıkar
     */
    private function extractOrderAmount(array $context): ?Money
    {
        if (isset($context['order_amount']) && $context['order_amount'] instanceof Money) {
            return $context['order_amount'];
        }

        if (isset($context['total_amount']) && $context['total_amount'] instanceof Money) {
            return $context['total_amount'];
        }

        return null;
    }

    /**
     * Context'ten ağırlık bilgisini çıkar
     */
    private function extractWeight(array $context): float
    {
        return (float) ($context['weight'] ?? $context['total_weight'] ?? 0);
    }

    /**
     * Ağırlık için geçerli tier'ı bul
     */
    private function findApplicableWeightTier(float $weight): ?array
    {
        $tiers = $this->costRules['tiers'] ?? [];
        
        foreach ($tiers as $tier) {
            $minWeight = $tier['min_weight'] ?? 0;
            $maxWeight = $tier['max_weight'] ?? PHP_FLOAT_MAX;
            
            if ($weight >= $minWeight && $weight <= $maxWeight) {
                return $tier;
            }
        }
        
        return null;
    }

    /**
     * Tutar için geçerli tier'ı bul
     */
    private function findApplicableAmountTier(Money $amount): ?array
    {
        $tiers = $this->costRules['tiers'] ?? [];
        
        foreach ($tiers as $tier) {
            $minAmount = $tier['min_amount'] ?? Money::zero();
            $maxAmount = $tier['max_amount'] ?? null;
            
            if ($amount->isGreaterThanOrEqual($minAmount)) {
                if (!$maxAmount || $amount->isLessThanOrEqual($maxAmount)) {
                    return $tier;
                }
            }
        }
        
        return null;
    }

    /**
     * Maliyet kurallarını validate et
     */
    private function validateCostRules(array $rules): array
    {
        $validTypes = ['free', 'fixed', 'weight', 'amount'];
        
        if (!isset($rules['type']) || !in_array($rules['type'], $validTypes)) {
            throw new \InvalidArgumentException('Invalid shipping cost rule type');
        }

        return $rules;
    }

    /**
     * Kargo ücret kurallarını getir
     */
    public function getCostRules(): array
    {
        return $this->costRules;
    }

    /**
     * Kargo ücret tipini getir
     */
    public function getCostType(): string
    {
        return $this->costRules['type'];
    }

    /**
     * Kuralın geçerli olup olmadığını kontrol et
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        // Shipping rule'ları genellikle tüm siparişler için geçerlidir
        return true;
    }

    /**
     * Kural adını getir
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Kural açıklamasını getir
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * Özel öncelik ayarla
     */
    public function withCustomPriority(int $priority): self
    {
        $this->priority = $priority;
        return $this;
    }
}
