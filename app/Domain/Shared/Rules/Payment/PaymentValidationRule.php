<?php

namespace App\Domain\Shared\Rules\Payment;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;

/**
 * PaymentValidationRule
 * Ödeme doğrulama kuralı - ödeme tutarı ve yöntem doğrulaması
 */
class PaymentValidationRule implements PaymentRuleInterface
{
    private int $priority;
    private Money $minPaymentAmount;
    private Money $maxPaymentAmount;
    private array $allowedMethods;
    private bool $requiresVerification;

    public function __construct(
        int $priority = 100,
        ?Money $minPaymentAmount = null,
        ?Money $maxPaymentAmount = null,
        array $allowedMethods = ['credit_card', 'bank_transfer', 'paypal'],
        bool $requiresVerification = false
    ) {
        $this->priority = $priority;
        $this->minPaymentAmount = $minPaymentAmount ?? Money::fromAmount(1, 'TRY');
        $this->maxPaymentAmount = $maxPaymentAmount ?? Money::fromAmount(50000, 'TRY');
        $this->allowedMethods = $allowedMethods;
        $this->requiresVerification = $requiresVerification;
    }

    public function applyRule(EntityInterface $entity, array $context = []): PaymentRuleResult
    {
        if (!$entity instanceof Order) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $paymentAmount = $context['amount'] ?? $entity->getTotalAmount();
        $paymentMethod = $context['method'] ?? null;

        // Tutar kontrolü
        if ($paymentAmount->isLessThan($this->minPaymentAmount)) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Payment amount is below minimum ({$this->minPaymentAmount->toArray()['amount_major']} {$this->minPaymentAmount->getCurrency()})",
                [
                    'payment_amount' => $paymentAmount->toArray(),
                    'min_amount' => $this->minPaymentAmount->toArray()
                ]
            );
        }

        if ($paymentAmount->isGreaterThan($this->maxPaymentAmount)) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Payment amount exceeds maximum ({$this->maxPaymentAmount->toArray()['amount_major']} {$this->maxPaymentAmount->getCurrency()})",
                [
                    'payment_amount' => $paymentAmount->toArray(),
                    'max_amount' => $this->maxPaymentAmount->toArray()
                ]
            );
        }

        // Ödeme yöntemi kontrolü
        if ($paymentMethod && !in_array($paymentMethod, $this->allowedMethods)) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Payment method '{$paymentMethod}' is not allowed",
                [
                    'requested_method' => $paymentMethod,
                    'allowed_methods' => $this->allowedMethods
                ]
            );
        }

        // Sipariş durumu kontrolü
        if (!in_array($entity->getStatus()->value, ['pending', 'confirmed'])) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Order status '{$entity->getStatus()->value}' does not allow payment",
                ['order_status' => $entity->getStatus()->value]
            );
        }

        // Ödeme durumu kontrolü
        if (in_array($entity->getPaymentStatus()->value, ['paid', 'refunded'])) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Order payment status '{$entity->getPaymentStatus()->value}' does not allow new payment",
                ['payment_status' => $entity->getPaymentStatus()->value]
            );
        }

        $requiredVerifications = [];
        $warnings = [];

        // Yüksek tutar için ek doğrulama gereksinimi
        $highAmountThreshold = Money::fromAmount(10000, $paymentAmount->getCurrency());
        if ($paymentAmount->isGreaterThan($highAmountThreshold)) {
            $requiredVerifications[] = 'identity_verification';
            $warnings[] = 'High amount payment requires identity verification';
        }

        // Uluslararası ödeme kontrolü
        if ($context['international'] ?? false) {
            $requiredVerifications[] = 'international_verification';
            $warnings[] = 'International payment requires additional verification';
        }

        // Genel doğrulama gereksinimi
        if ($this->requiresVerification) {
            $requiredVerifications[] = 'general_verification';
        }

        return PaymentRuleResult::allowed(
            $this->getName(),
            $paymentAmount,
            $this->allowedMethods,
            'Payment validation passed',
            [
                'payment_amount' => $paymentAmount->toArray(),
                'order_id' => $entity->getId(),
                'verification_required' => !empty($requiredVerifications),
                'required_verifications' => $requiredVerifications
            ]
        )->withWarnings($warnings);
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'payment_validation';
    }

    public function getDescription(): string
    {
        return 'Validates payment amount, method, and order status for payment processing';
    }

    /**
     * Minimum ödeme tutarını ayarla
     */
    public function setMinPaymentAmount(Money $amount): self
    {
        $this->minPaymentAmount = $amount;
        return $this;
    }

    /**
     * Minimum ödeme tutarını ayarla (alias)
     */
    public function setMinimumPaymentAmount(Money $amount): self
    {
        return $this->setMinPaymentAmount($amount);
    }

    /**
     * Maksimum ödeme tutarını ayarla
     */
    public function setMaxPaymentAmount(Money $amount): self
    {
        $this->maxPaymentAmount = $amount;
        return $this;
    }

    /**
     * İzin verilen ödeme yöntemlerini ayarla
     */
    public function setAllowedMethods(array $methods): self
    {
        $this->allowedMethods = $methods;
        return $this;
    }

    /**
     * Doğrulama gerekliliğini ayarla
     */
    public function setRequiresVerification(bool $requires): self
    {
        $this->requiresVerification = $requires;
        return $this;
    }

    /**
     * Ödeme yönteminin izin verilip verilmediğini kontrol et
     */
    public function isMethodAllowed(string $method): bool
    {
        return in_array($method, $this->allowedMethods);
    }

    /**
     * Tutarın limitler içinde olup olmadığını kontrol et
     */
    public function isAmountValid(Money $amount): bool
    {
        return $amount->isGreaterThanOrEqualTo($this->minPaymentAmount) &&
               $amount->isLessThanOrEqualTo($this->maxPaymentAmount);
    }

    /**
     * Ödeme limitlerini getir
     */
    public function getPaymentLimits(): array
    {
        return [
            'min_amount' => $this->minPaymentAmount->toArray(),
            'max_amount' => $this->maxPaymentAmount->toArray(),
            'allowed_methods' => $this->allowedMethods,
            'requires_verification' => $this->requiresVerification,
        ];
    }

    /**
     * Standart ödeme doğrulama kuralı oluştur
     */
    public static function standard(): self
    {
        return new self(
            Money::fromAmount(1, 'TRY'), // Minimum 1 TL
            ['credit_card', 'bank_transfer', 'paypal', 'cash']
        );
    }

    /**
     * Sıkı ödeme doğrulama kuralı oluştur
     */
    public static function strict(): self
    {
        $rule = new self(
            Money::fromAmount(10, 'TRY'), // Minimum 10 TL
            ['credit_card', 'bank_transfer']
        );

        $rule->setMaxPaymentAmount(Money::fromAmount(10000, 'TRY')); // Maksimum 10,000 TL
        $rule->setRequiresVerification(true);

        return $rule;
    }

    /**
     * Express ödeme doğrulama kuralı oluştur
     */
    public static function express(): self
    {
        return new self(
            Money::fromAmount(5, 'TRY'), // Minimum 5 TL
            ['credit_card', 'paypal', 'apple_pay', 'google_pay']
        );
    }
}
