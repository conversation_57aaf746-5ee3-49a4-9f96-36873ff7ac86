<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;

/**
 * OrderValidationRule
 * Sipariş doğrulama kuralı
 */
class OrderValidationRule implements OrderRuleInterface
{
    private Money $minimumOrderAmount;
    private array $allowedStatuses;
    private int $priority;
    private bool $requireCustomerVerification;
    private bool $requireInventoryCheck;

    public function __construct(
        Money $minimumOrderAmount = null,
        array $allowedStatuses = ['pending', 'confirmed', 'processing'],
        int $priority = 100,
        bool $requireCustomerVerification = true,
        bool $requireInventoryCheck = true
    ) {
        $this->minimumOrderAmount = $minimumOrderAmount ?? Money::fromAmount(1, 'TRY');
        $this->allowedStatuses = $allowedStatuses;
        $this->priority = $priority;
        $this->requireCustomerVerification = $requireCustomerVerification;
        $this->requireInventoryCheck = $requireInventoryCheck;
    }

    public function applyRule(EntityInterface $entity, array $context = []): OrderRuleResult
    {
        if (!$entity instanceof Order) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $errors = [];
        $warnings = [];
        $requiredActions = [];

        // Minimum tutar kontrolü
        if ($entity->getTotalAmount()->isLessThan($this->minimumOrderAmount)) {
            $errors[] = "Order amount ({$entity->getTotalAmount()->toArray()['amount_major']} {$entity->getTotalAmount()->getCurrency()}) is below minimum ({$this->minimumOrderAmount->toArray()['amount_major']} {$this->minimumOrderAmount->getCurrency()})";
        }

        // Sipariş durumu kontrolü
        if (!in_array($entity->getStatus(), $this->allowedStatuses)) {
            $errors[] = "Order status '{$entity->getStatus()}' is not allowed for this operation";
        }

        // Müşteri doğrulama kontrolü
        if ($this->requireCustomerVerification && !$this->isCustomerVerified($entity, $context)) {
            $requiredActions[] = 'customer_verification';
            $warnings[] = 'Customer verification required';
        }

        // Stok kontrolü
        if ($this->requireInventoryCheck && !$this->isInventoryAvailable($entity, $context)) {
            $errors[] = 'Insufficient inventory for order items';
        }

        // Sipariş öğeleri kontrolü
        if ($entity->getItems()->isEmpty()) {
            $errors[] = 'Order must contain at least one item';
        }

        // Teslimat adresi kontrolü
        if (!$entity->getShippingAddress()) {
            $errors[] = 'Shipping address is required';
        }

        // Ödeme yöntemi kontrolü
        if (!$entity->getPaymentMethod()) {
            $requiredActions[] = 'payment_method_selection';
            $warnings[] = 'Payment method must be selected';
        }

        // Sonuç değerlendirmesi
        if (!empty($errors)) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Order validation failed: ' . implode(', ', $errors),
                [
                    'errors' => $errors,
                    'warnings' => $warnings,
                    'order_id' => $entity->getId(),
                    'total_amount' => $entity->getTotalAmount()->toArray(),
                    'status' => $entity->getStatus()
                ]
            );
        }

        if (!empty($requiredActions)) {
            return OrderRuleResult::requiresAction(
                $this->getName(),
                $requiredActions,
                'Order validation passed but requires additional actions',
                [
                    'warnings' => $warnings,
                    'order_id' => $entity->getId(),
                    'total_amount' => $entity->getTotalAmount()->toArray(),
                    'status' => $entity->getStatus()
                ]
            );
        }

        return OrderRuleResult::allowed(
            $this->getName(),
            null,
            [],
            'Order validation passed successfully',
            [
                'order_id' => $entity->getId(),
                'total_amount' => $entity->getTotalAmount()->toArray(),
                'status' => $entity->getStatus(),
                'items_count' => $entity->getItems()->count()
            ]
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'order_validation';
    }

    public function getDescription(): string
    {
        return 'Validates order requirements including minimum amount, status, customer verification, and inventory availability';
    }

    /**
     * Müşteri doğrulaması yapılmış mı kontrol et
     */
    private function isCustomerVerified(Order $order, array $context): bool
    {
        // Context'ten doğrulama bilgisini al
        if (isset($context['customer_verified'])) {
            return $context['customer_verified'];
        }

        // Müşteri bilgilerinin varlığını kontrol et
        $customer = $order->getCustomer();
        if (!$customer) {
            return false;
        }

        // E-posta doğrulaması
        if (!$customer->isEmailVerified()) {
            return false;
        }

        // Telefon doğrulaması (yüksek tutarlı siparişler için)
        if ($order->getTotalAmount()->isGreaterThan(Money::fromAmount(1000, $order->getTotalAmount()->getCurrency()))) {
            return $customer->isPhoneVerified();
        }

        return true;
    }

    /**
     * Stok durumunu kontrol et
     */
    private function isInventoryAvailable(Order $order, array $context): bool
    {
        // Context'ten stok bilgisini al
        if (isset($context['inventory_checked'])) {
            return $context['inventory_checked'];
        }

        // Sipariş öğelerinin stok durumunu kontrol et
        foreach ($order->getItems() as $item) {
            $product = $item->getProduct();
            $requestedQuantity = $item->getQuantity();

            // Stok kontrolü
            if ($product->getStockQuantity() < $requestedQuantity) {
                return false;
            }

            // Aktif ürün kontrolü
            if (!$product->isActive()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Minimum sipariş tutarını ayarla
     */
    public function setMinimumOrderAmount(Money $amount): self
    {
        $this->minimumOrderAmount = $amount;
        return $this;
    }

    /**
     * İzin verilen durumları ayarla
     */
    public function setAllowedStatuses(array $statuses): self
    {
        $this->allowedStatuses = $statuses;
        return $this;
    }

    /**
     * Müşteri doğrulama gereksinimini ayarla
     */
    public function setRequireCustomerVerification(bool $require): self
    {
        $this->requireCustomerVerification = $require;
        return $this;
    }

    /**
     * Stok kontrolü gereksinimini ayarla
     */
    public function setRequireInventoryCheck(bool $require): self
    {
        $this->requireInventoryCheck = $require;
        return $this;
    }

    /**
     * Müşteri doğrulama gereksinimini devre dışı bırak
     */
    public function disableCustomerVerification(): self
    {
        $this->requireCustomerVerification = false;
        return $this;
    }

    /**
     * Stok kontrolünü devre dışı bırak
     */
    public function disableInventoryCheck(): self
    {
        $this->requireInventoryCheck = false;
        return $this;
    }

    /**
     * Standart sipariş doğrulama kuralı
     */
    public static function standard(): self
    {
        return new self(
            Money::fromAmount(10, 'TRY'), // Minimum 10 TRY
            ['pending', 'confirmed', 'processing'],
            100,
            true,
            true
        );
    }

    /**
     * Hızlı sipariş doğrulama kuralı (daha az kontrol)
     */
    public static function express(): self
    {
        return new self(
            Money::fromAmount(1, 'TRY'), // Minimum 1 TRY
            ['pending', 'confirmed'],
            50,
            false, // Müşteri doğrulama gerekmiyor
            true
        );
    }
}
