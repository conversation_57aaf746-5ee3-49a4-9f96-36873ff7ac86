<?php

namespace App\Domain\Shipping\Repositories;

use App\Domain\Shipping\Entities\Shipment;
use App\Domain\Shipping\ValueObjects\TrackingNumber;
use App\Domain\Shipping\ValueObjects\ShippingAddress;

/**
 * ShipmentRepositoryInterface
 * Shipment repository interface
 */
interface ShipmentRepositoryInterface
{
    /**
     * ID ile shipment bul
     */
    public function findById(int $id): ?Shipment;

    /**
     * Tracking number ile shipment bul
     */
    public function findByTrackingNumber(TrackingNumber $trackingNumber): ?Shipment;

    /**
     * Order ID ile shipment'ları bul
     */
    public function findByOrderId(int $orderId): array;

    /**
     * Carrier ile shipment'ları bul
     */
    public function findByCarrier(string $carrierCode): array;

    /**
     * Status ile shipment'ları bul
     */
    public function findByStatus(string $status): array;

    /**
     * Tarih aralığında shipment'ları bul
     */
    public function findByDateRange(\DateTime $startDate, \DateTime $endDate): array;

    /**
     * Adres ile shipment'ları bul
     */
    public function findByAddress(ShippingAddress $address): array;

    /**
     * Aktif shipment'ları bul
     */
    public function findActive(): array;

    /**
     * Teslim edilmiş shipment'ları bul
     */
    public function findDelivered(): array;

    /**
     * Bekleyen shipment'ları bul
     */
    public function findPending(): array;

    /**
     * Shipment kaydet
     */
    public function save(Shipment $shipment): void;

    /**
     * Shipment sil
     */
    public function delete(Shipment $shipment): void;

    /**
     * Toplu shipment kaydet
     */
    public function saveBatch(array $shipments): void;

    /**
     * Shipment sayısını getir
     */
    public function count(): int;

    /**
     * Sayfalı shipment listesi getir
     */
    public function paginate(int $page = 1, int $perPage = 20, array $filters = []): array;

    /**
     * Shipment arama
     */
    public function search(string $query, array $filters = []): array;

    /**
     * Shipment istatistikleri getir
     */
    public function getStatistics(): array;

    /**
     * Carrier bazlı özet getir
     */
    public function getCarrierSummary(): array;

    /**
     * Status bazlı özet getir
     */
    public function getStatusSummary(): array;

    /**
     * Tracking geçmişi getir
     */
    public function getTrackingHistory(int $shipmentId): array;

    /**
     * Delivery attempts getir
     */
    public function getDeliveryAttempts(int $shipmentId): array;

    /**
     * Shipment notları getir
     */
    public function getNotes(int $shipmentId): array;

    /**
     * Geciken shipment'ları getir
     */
    public function getDelayedShipments(): array;

    /**
     * Performans raporu getir
     */
    public function getPerformanceReport(\DateTime $startDate, \DateTime $endDate): array;

    /**
     * Maliyet analizi getir
     */
    public function getCostAnalysis(\DateTime $startDate, \DateTime $endDate): array;

    /**
     * Carrier performans karşılaştırması getir
     */
    public function getCarrierComparison(\DateTime $startDate, \DateTime $endDate): array;
}
