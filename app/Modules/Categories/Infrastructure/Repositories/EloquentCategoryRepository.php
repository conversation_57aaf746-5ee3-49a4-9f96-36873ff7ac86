<?php

namespace App\Modules\Categories\Infrastructure\Repositories;

use App\Modules\Categories\Domain\Interfaces\CategoryRepositoryInterface;
use App\Models\Category;
use Illuminate\Database\Eloquent\Collection;

/**
 * EloquentCategoryRepository
 * Category için Eloquent repository implementation
 */
class EloquentCategoryRepository implements CategoryRepositoryInterface
{

    // Category-specific methods
    public function findBySlug(string $slug): ?Category
    {
        return Category::where('slug', $slug)->first();
    }

    public function findRootCategories(array $columns = ['*']): Collection
    {
        return Category::whereNull('parent_id')->select($columns)->get();
    }

    public function findByParent(int $parentId, array $columns = ['*']): Collection
    {
        return Category::where('parent_id', $parentId)->select($columns)->get();
    }

    public function findActive(array $columns = ['*']): Collection
    {
        return Category::where('status', true)->select($columns)->get();
    }

    public function findFeatured(int $limit = 10, array $columns = ['*']): Collection
    {
        return Category::where('is_featured', true)
            ->select($columns)
            ->limit($limit)
            ->get();
    }

    public function findShownInMenu(array $columns = ['*']): Collection
    {
        return Category::where('show_in_menu', true)->select($columns)->get();
    }

    public function getCategoryTree(bool $activeOnly = false): array
    {
        $query = Category::whereNull('parent_id')->with('children');

        if ($activeOnly) {
            $query->where('status', true);
        }

        return $query->get()->toArray();
    }

    public function getCategoryHierarchy(int $categoryId): \Illuminate\Support\Collection
    {
        $hierarchy = collect();
        $category = Category::find($categoryId);

        while ($category) {
            $hierarchy->prepend($category);
            $category = $category->parent;
        }

        return $hierarchy;
    }

    public function findByPosition(?int $parentId = null, array $columns = ['*']): Collection
    {
        return Category::where('parent_id', $parentId)
            ->orderBy('position')
            ->select($columns)
            ->get();
    }

    public function isSlugUnique(string $slug, ?int $excludeId = null): bool
    {
        $query = Category::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    public function canBeDeleted(int $categoryId): bool
    {
        // Kategori alt kategorilere sahipse silinemez
        return !Category::where('parent_id', $categoryId)->exists();
    }

    public function getMaxPosition(?int $parentId = null): int
    {
        return Category::where('parent_id', $parentId)->max('position') ?? 0;
    }

    public function reorderCategories(array $positions): bool
    {
        try {
            foreach ($positions as $position) {
                Category::where('id', $position['id'])
                    ->update(['position' => $position['position']]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function findByMaxDepth(int $maxDepth, array $columns = ['*']): Collection
    {
        // Bu basit implementation depth kontrolü yapmıyor
        // Gerçek implementasyonda recursive query gerekir
        return Category::select($columns)->get();
    }

    public function findWithMinProductCount(int $minProductCount, array $columns = ['*']): Collection
    {
        return Category::select($columns)
            ->withCount('products')
            ->having('products_count', '>=', $minProductCount)
            ->get();
    }

    /**
     * Yeni kategori oluştur
     */
    public function create(array $data): Category
    {
        return Category::create($data);
    }

    /**
     * Kategoriyi güncelle
     */
    public function update(int $id, array $data): ?Category
    {
        $category = Category::find($id);
        if ($category) {
            $category->update($data);
            return $category->fresh();
        }
        return null;
    }

    /**
     * Kategoriyi sil
     */
    public function delete(int $id): bool
    {
        $category = Category::find($id);
        return $category ? $category->delete() : false;
    }

    /**
     * ID'ye göre kategori getir
     */
    public function findById(int $id): ?Category
    {
        return Category::find($id);
    }

    /**
     * Tüm kategorileri getir
     */
    public function findAll(array $columns = ['*']): Collection
    {
        return Category::select($columns)->get();
    }
}
