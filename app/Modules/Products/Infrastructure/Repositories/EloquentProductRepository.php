<?php

namespace App\Modules\Products\Infrastructure\Repositories;

use App\Modules\Products\Domain\Interfaces\ProductRepositoryInterface;
use App\Domain\Products\Entities\Product as DomainProduct;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * EloquentProductRepository
 * Product için Eloquent repository implementation
 */
class EloquentProductRepository implements ProductRepositoryInterface
{
    /**
     * Slug'a göre ürün getir
     */
    public function findBySlug(string $slug): ?Product
    {
        return Product::where('slug', $slug)->first();
    }

    /**
     * SKU'ya göre ürün getir
     */
    public function findBySku(string $sku): ?Product
    {
        return Product::where('sku', $sku)->first();
    }

    /**
     * Aktif ürünleri getir
     */
    public function findActive(array $columns = ['*']): Collection
    {
        return Product::where('status', true)->select($columns)->get();
    }

    /**
     * Öne çıkarılmış ürünleri getir
     */
    public function findFeatured(int $limit = 10, array $columns = ['*']): Collection
    {
        return Product::where('is_featured', true)
            ->select($columns)
            ->limit($limit)
            ->get();
    }

    /**
     * Kategoriye göre ürünleri getir
     */
    public function findByCategory(int $categoryId, array $columns = ['*']): Collection
    {
        return Product::where('category_id', $categoryId)
            ->select($columns)
            ->get();
    }

    /**
     * Fiyat aralığına göre ürünleri getir
     */
    public function findByPriceRange(float $minPrice, float $maxPrice, array $columns = ['*']): Collection
    {
        return Product::whereBetween('price', [$minPrice, $maxPrice])
            ->select($columns)
            ->get();
    }

    /**
     * Stokta olan ürünleri getir
     */
    public function findInStock(array $columns = ['*']): Collection
    {
        return Product::where('stock_quantity', '>', 0)
            ->select($columns)
            ->get();
    }

    /**
     * Ürün arama
     */
    public function search(string $query, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $queryBuilder = Product::where('name', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->orWhere('sku', 'like', "%{$query}%");

        // Apply filters
        foreach ($filters as $field => $value) {
            if ($value !== null) {
                $queryBuilder->where($field, $value);
            }
        }

        return $queryBuilder->paginate($perPage);
    }

    /**
     * En çok görüntülenen ürünleri getir
     */
    public function findMostViewed(int $limit = 10, array $columns = ['*']): Collection
    {
        return Product::orderBy('view_count', 'desc')
            ->select($columns)
            ->limit($limit)
            ->get();
    }

    /**
     * Son eklenen ürünleri getir
     */
    public function findLatest(int $limit = 10, array $columns = ['*']): Collection
    {
        return Product::orderBy('created_at', 'desc')
            ->select($columns)
            ->limit($limit)
            ->get();
    }

    /**
     * İndirimli ürünleri getir
     */
    public function findOnSale(int $limit = 10, array $columns = ['*']): Collection
    {
        return Product::whereNotNull('sale_price')
            ->where('sale_price', '>', 0)
            ->select($columns)
            ->limit($limit)
            ->get();
    }

    /**
     * SKU'nun benzersiz olup olmadığını kontrol et
     */
    public function isSkuUnique(string $sku, ?int $excludeId = null): bool
    {
        $query = Product::where('sku', $sku);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Slug'ın benzersiz olup olmadığını kontrol et
     */
    public function isSlugUnique(string $slug, ?int $excludeId = null): bool
    {
        $query = Product::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Yeni ürün oluştur
     */
    public function create(array $data): DomainProduct
    {
        $eloquentProduct = Product::create($data);
        return $this->mapToDomainModel($eloquentProduct);
    }

    /**
     * Ürünü güncelle
     */
    public function update(int $id, array $data): ?Product
    {
        $product = Product::find($id);
        if ($product) {
            $product->update($data);
            return $product->fresh();
        }
        return null;
    }

    /**
     * Ürünü sil
     */
    public function delete(int $id): bool
    {
        $product = Product::find($id);
        return $product ? $product->delete() : false;
    }

    /**
     * ID'ye göre ürün getir
     */
    public function findById(int $id): ?Product
    {
        return Product::find($id);
    }

    /**
     * Tüm ürünleri getir
     */
    public function findAll(array $columns = ['*']): Collection
    {
        return Product::select($columns)->get();
    }

    /**
     * Eloquent model'i domain model'e çevir
     */
    private function mapToDomainModel(Product $eloquentProduct): DomainProduct
    {
        $product = DomainProduct::create(
            name: $eloquentProduct->name,
            slug: $eloquentProduct->slug,
            sku: SKU::fromString($eloquentProduct->sku ?? 'AUTO-' . time()),
            price: Price::fromAmount($eloquentProduct->price ?? 0, 'TRY'),
            stock: Stock::fromQuantity($eloquentProduct->stock ?? 0),
            categoryId: $eloquentProduct->category_id ?? 1,
            description: $eloquentProduct->description,
            status: $eloquentProduct->status ?? true,
            isFeatured: $eloquentProduct->is_featured ?? false
        );

        // ID'yi set et
        $product->setId($eloquentProduct->id);

        return $product;
    }
}
