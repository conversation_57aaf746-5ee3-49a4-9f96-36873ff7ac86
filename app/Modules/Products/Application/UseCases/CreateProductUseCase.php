<?php

namespace App\Modules\Products\Application\UseCases;

use App\Core\Application\UseCase;
use App\Modules\Products\Application\DTOs\CreateProductDTO;
use App\Modules\Products\Domain\Interfaces\ProductRepositoryInterface;
use App\Domain\Products\Entities\Product;
use App\Modules\Products\Domain\ValueObjects\ProductSku;
use App\Modules\Products\Domain\ValueObjects\ProductSlug;
use Illuminate\Support\Str;

/**
 * Create Product Use Case
 * Ürün oluşturma use case'i
 */
class CreateProductUseCase extends UseCase
{
    /**
     * Product repository
     *
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * Constructor
     *
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Use case'i çalıştır
     *
     * @param CreateProductDTO $dto
     * @return Product
     * @throws \InvalidArgumentException
     */
    public function execute($dto = null): Product
    {
        if (!$dto instanceof CreateProductDTO) {
            throw new \InvalidArgumentException('Invalid input type. Expected CreateProductDTO.');
        }

        // Slug oluştur ve benzersizlik kontrolü yap
        $slug = $this->generateUniqueSlug($dto->name);

        // SKU kontrolü ve oluşturma
        $sku = $this->handleSku($dto->sku);

        // Model için veri hazırla
        $productData = $dto->toModelArray();
        $productData['slug'] = $slug;
        $productData['sku'] = $sku;

        // Ürünü oluştur
        $product = $this->productRepository->create($productData);

        return $product;
    }

    /**
     * Input'u validate et
     *
     * @param mixed $input
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateInput($input): void
    {
        if (!$input instanceof CreateProductDTO) {
            throw new \InvalidArgumentException('Input must be an instance of CreateProductDTO');
        }

        // DTO kendi validation'ını yapar, burada ek business rule'lar kontrol edilebilir
    }

    /**
     * Benzersiz slug oluştur
     *
     * @param string $name
     * @return string
     */
    private function generateUniqueSlug(string $name): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        // Slug benzersiz olana kadar dene
        while (!$this->productRepository->isSlugUnique($slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        // ProductSlug value object ile validate et
        $slugValueObject = new ProductSlug($slug);
        
        return $slugValueObject->getValue();
    }

    /**
     * SKU'yu işle
     *
     * @param string|null $sku
     * @return string|null
     * @throws \InvalidArgumentException
     */
    private function handleSku(?string $sku): ?string
    {
        if (empty($sku)) {
            // SKU verilmemişse otomatik oluştur
            $sku = $this->generateUniqueSku();
        } else {
            // SKU verilmişse validate et ve benzersizlik kontrol et
            $skuValueObject = new ProductSku($sku);
            $sku = $skuValueObject->getValue();

            if (!$this->productRepository->isSkuUnique($sku)) {
                throw new \InvalidArgumentException("SKU '{$sku}' already exists");
            }
        }

        return $sku;
    }

    /**
     * Benzersiz SKU oluştur
     *
     * @return string
     */
    private function generateUniqueSku(): string
    {
        $maxAttempts = 10;
        $attempts = 0;

        do {
            $sku = ProductSku::generate()->getValue();
            $attempts++;
        } while (!$this->productRepository->isSkuUnique($sku) && $attempts < $maxAttempts);

        if ($attempts >= $maxAttempts) {
            throw new \RuntimeException('Unable to generate unique SKU after maximum attempts');
        }

        return $sku;
    }
}
