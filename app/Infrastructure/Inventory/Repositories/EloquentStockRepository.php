<?php

namespace App\Infrastructure\Inventory\Repositories;

use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\Repositories\StockRepositoryInterface;
use App\Domain\Inventory\ValueObjects\StockLevel;
use App\Domain\Inventory\ValueObjects\ReservationId;
use App\Domain\Inventory\ValueObjects\StockLocation;
use App\Infrastructure\Inventory\Models\EloquentStock;
use App\Infrastructure\Inventory\Models\EloquentStockLocation;
use App\Infrastructure\Inventory\Mappers\StockMapper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * EloquentStockRepository
 * Eloquent tabanlı stok repository implementasyonu
 */
class EloquentStockRepository implements StockRepositoryInterface
{
    public function __construct(
        private StockMapper $mapper
    ) {}

    /**
     * ID ile stok bul
     */
    public function findById(int $id): ?Stock
    {
        try {
            $eloquentStock = EloquentStock::with(['location', 'product', 'productVariant'])
                ->find($id);

            return $eloquentStock ? $this->mapper->toDomain($eloquentStock) : null;
        } catch (\Exception $e) {
            Log::error('Failed to find stock by ID', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Ürün için stok bul
     */
    public function findByProduct(int $productId, ?int $productVariantId = null, ?StockLocation $location = null): ?Stock
    {
        try {
            $query = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('product_id', $productId);

            if ($productVariantId) {
                $query->where('product_variant_id', $productVariantId);
            } else {
                $query->whereNull('product_variant_id');
            }

            if ($location) {
                $query->whereHas('location', function ($q) use ($location) {
                    $q->where('code', $location->getCode());
                });
            }

            $eloquentStock = $query->first();

            return $eloquentStock ? $this->mapper->toDomain($eloquentStock) : null;
        } catch (\Exception $e) {
            Log::error('Failed to find stock by product', [
                'product_id' => $productId,
                'product_variant_id' => $productVariantId,
                'location' => $location?->getCode(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Ürün için tüm stokları bul
     */
    public function findAllByProduct(int $productId, ?int $productVariantId = null): array
    {
        try {
            $query = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('product_id', $productId);

            if ($productVariantId) {
                $query->where('product_variant_id', $productVariantId);
            } else {
                $query->whereNull('product_variant_id');
            }

            $eloquentStocks = $query->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find all stocks by product', [
                'product_id' => $productId,
                'product_variant_id' => $productVariantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Ürün için mevcut stokları bul
     */
    public function findAvailableByProduct(int $productId, ?int $productVariantId = null): array
    {
        try {
            $query = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('product_id', $productId)
                ->where('is_active', true)
                ->where('available_quantity', '>', 0);

            if ($productVariantId) {
                $query->where('product_variant_id', $productVariantId);
            } else {
                $query->whereNull('product_variant_id');
            }

            $eloquentStocks = $query->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find available stocks by product', [
                'product_id' => $productId,
                'product_variant_id' => $productVariantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Rezervasyon ID ile stok bul
     */
    public function findByReservationId(ReservationId $reservationId): ?Stock
    {
        try {
            $eloquentStock = EloquentStock::with(['location', 'product', 'productVariant'])
                ->whereJsonContains('reservations', ['reservation_id' => $reservationId->getValue()])
                ->first();

            return $eloquentStock ? $this->mapper->toDomain($eloquentStock) : null;
        } catch (\Exception $e) {
            Log::error('Failed to find stock by reservation ID', [
                'reservation_id' => $reservationId->getValue(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Lokasyon ile stokları bul
     */
    public function findByLocation(StockLocation $location): array
    {
        try {
            $eloquentStocks = EloquentStock::with(['location', 'product', 'productVariant'])
                ->whereHas('location', function ($q) use ($location) {
                    $q->where('code', $location->getCode());
                })
                ->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find stocks by location', [
                'location' => $location->getCode(),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Düşük stok seviyesindeki stokları bul
     */
    public function findLowStock(): array
    {
        try {
            $eloquentStocks = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('is_active', true)
                ->where('track_inventory', true)
                ->whereRaw('available_quantity <= low_stock_threshold')
                ->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find low stock items', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Yeniden sipariş gereken stokları bul
     */
    public function findNeedingReorder(): array
    {
        try {
            $eloquentStocks = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('is_active', true)
                ->where('track_inventory', true)
                ->whereRaw('available_quantity <= reorder_level')
                ->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find stocks needing reorder', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stokta olmayan ürünleri bul
     */
    public function findOutOfStock(): array
    {
        try {
            $eloquentStocks = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('is_active', true)
                ->where('track_inventory', true)
                ->where('available_quantity', '<=', 0)
                ->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find out of stock items', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Süresi dolmuş rezervasyonları bul
     */
    public function findExpiredReservations(int $maxAgeSeconds = 3600): array
    {
        try {
            $cutoffTime = Carbon::now()->subSeconds($maxAgeSeconds);

            $eloquentStocks = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('reserved_quantity', '>', 0)
                ->whereNotNull('reservations')
                ->get()
                ->filter(function ($stock) use ($cutoffTime) {
                    $reservations = json_decode($stock->reservations, true) ?? [];
                    foreach ($reservations as $reservation) {
                        $reservedAt = Carbon::parse($reservation['reserved_at'] ?? null);
                        if ($reservedAt->lt($cutoffTime)) {
                            return true;
                        }
                    }
                    return false;
                });

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find expired reservations', [
                'max_age_seconds' => $maxAgeSeconds,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Aktif rezervasyonları bul
     */
    public function findActiveReservations(): array
    {
        try {
            $eloquentStocks = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('reserved_quantity', '>', 0)
                ->whereNotNull('reservations')
                ->get();

            return $eloquentStocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find active reservations', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Belirli ürünler için stok seviyelerini bul
     */
    public function findStockLevelsForProducts(array $productIds): array
    {
        try {
            $results = EloquentStock::select([
                'product_id',
                'product_variant_id',
                'location_id',
                'available_quantity',
                'reserved_quantity',
                'total_quantity'
            ])
            ->whereIn('product_id', $productIds)
            ->where('is_active', true)
            ->get()
            ->groupBy('product_id')
            ->map(function ($stocks) {
                return $stocks->map(function ($stock) {
                    return [
                        'product_variant_id' => $stock->product_variant_id,
                        'location_id' => $stock->location_id,
                        'available_quantity' => $stock->available_quantity,
                        'reserved_quantity' => $stock->reserved_quantity,
                        'total_quantity' => $stock->total_quantity,
                    ];
                })->toArray();
            })
            ->toArray();

            return $results;
        } catch (\Exception $e) {
            Log::error('Failed to find stock levels for products', [
                'product_ids' => $productIds,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok kaydet
     */
    public function save(Stock $stock): void
    {
        try {
            DB::transaction(function () use ($stock) {
                $eloquentStock = $this->mapper->toEloquent($stock);
                $eloquentStock->save();

                // ID'yi domain entity'ye set et
                if (!$stock->getId()) {
                    $stock->setId($eloquentStock->id);
                }
            });

            Log::info('Stock saved successfully', [
                'stock_id' => $stock->getId(),
                'product_id' => $stock->getProductId(),
                'available_quantity' => $stock->getAvailableQuantity()->getValue()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to save stock', [
                'stock_id' => $stock->getId(),
                'product_id' => $stock->getProductId(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Stok sil
     */
    public function delete(Stock $stock): void
    {
        try {
            DB::transaction(function () use ($stock) {
                EloquentStock::where('id', $stock->getId())->delete();
            });

            Log::info('Stock deleted successfully', [
                'stock_id' => $stock->getId(),
                'product_id' => $stock->getProductId()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete stock', [
                'stock_id' => $stock->getId(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Toplu stok kaydet
     */
    public function saveBatch(array $stocks): void
    {
        try {
            DB::transaction(function () use ($stocks) {
                foreach ($stocks as $stock) {
                    $this->save($stock);
                }
            });

            Log::info('Batch stock save completed', [
                'count' => count($stocks)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to save stock batch', [
                'count' => count($stocks),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Stok sayısını getir
     */
    public function count(): int
    {
        try {
            return EloquentStock::where('is_active', true)->count();
        } catch (\Exception $e) {
            Log::error('Failed to count stocks', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Sayfalı stok listesi getir
     */
    public function paginate(int $page = 1, int $perPage = 20, array $filters = []): array
    {
        try {
            $query = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('is_active', true);

            // Filtreleri uygula
            if (!empty($filters['product_id'])) {
                $query->where('product_id', $filters['product_id']);
            }

            if (!empty($filters['location_id'])) {
                $query->where('location_id', $filters['location_id']);
            }

            if (!empty($filters['low_stock'])) {
                $query->whereRaw('available_quantity <= low_stock_threshold');
            }

            if (!empty($filters['out_of_stock'])) {
                $query->where('available_quantity', '<=', 0);
            }

            if (!empty($filters['track_inventory'])) {
                $query->where('track_inventory', $filters['track_inventory']);
            }

            $total = $query->count();
            $stocks = $query->skip(($page - 1) * $perPage)
                          ->take($perPage)
                          ->get();

            return [
                'data' => $stocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray(),
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => min($page * $perPage, $total)
            ];
        } catch (\Exception $e) {
            Log::error('Failed to paginate stocks', [
                'page' => $page,
                'per_page' => $perPage,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'data' => [],
                'total' => 0,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => 0,
                'from' => 0,
                'to' => 0
            ];
        }
    }

    /**
     * Stok arama
     */
    public function search(string $query, array $filters = []): array
    {
        try {
            $searchQuery = EloquentStock::with(['location', 'product', 'productVariant'])
                ->where('is_active', true);

            // Ürün adı veya SKU ile arama
            $searchQuery->where(function ($q) use ($query) {
                $q->whereHas('product', function ($productQuery) use ($query) {
                    $productQuery->where('name', 'like', "%{$query}%")
                               ->orWhere('sku', 'like', "%{$query}%");
                })
                ->orWhereHas('productVariant', function ($variantQuery) use ($query) {
                    $variantQuery->where('sku', 'like', "%{$query}%");
                });
            });

            // Filtreleri uygula
            if (!empty($filters['location_id'])) {
                $searchQuery->where('location_id', $filters['location_id']);
            }

            if (!empty($filters['category_id'])) {
                $searchQuery->whereHas('product', function ($q) use ($filters) {
                    $q->where('category_id', $filters['category_id']);
                });
            }

            $stocks = $searchQuery->limit(50)->get();

            return $stocks->map(fn($stock) => $this->mapper->toDomain($stock))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to search stocks', [
                'query' => $query,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok istatistikleri getir
     */
    public function getStatistics(): array
    {
        try {
            $stats = DB::select("
                SELECT
                    COUNT(*) as total_stocks,
                    SUM(available_quantity) as total_available,
                    SUM(reserved_quantity) as total_reserved,
                    SUM(total_quantity) as total_inventory,
                    COUNT(CASE WHEN available_quantity <= low_stock_threshold THEN 1 END) as low_stock_count,
                    COUNT(CASE WHEN available_quantity <= 0 THEN 1 END) as out_of_stock_count,
                    COUNT(CASE WHEN available_quantity <= reorder_level THEN 1 END) as reorder_needed_count,
                    AVG(available_quantity) as avg_available_quantity
                FROM stocks
                WHERE is_active = 1 AND track_inventory = 1
            ");

            return [
                'total_stocks' => (int) $stats[0]->total_stocks,
                'total_available' => (int) $stats[0]->total_available,
                'total_reserved' => (int) $stats[0]->total_reserved,
                'total_inventory' => (int) $stats[0]->total_inventory,
                'low_stock_count' => (int) $stats[0]->low_stock_count,
                'out_of_stock_count' => (int) $stats[0]->out_of_stock_count,
                'reorder_needed_count' => (int) $stats[0]->reorder_needed_count,
                'avg_available_quantity' => round((float) $stats[0]->avg_available_quantity, 2),
                'stock_health_percentage' => $stats[0]->total_stocks > 0
                    ? round((($stats[0]->total_stocks - $stats[0]->low_stock_count) / $stats[0]->total_stocks) * 100, 2)
                    : 0
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get stock statistics', [
                'error' => $e->getMessage()
            ]);
            return [
                'total_stocks' => 0,
                'total_available' => 0,
                'total_reserved' => 0,
                'total_inventory' => 0,
                'low_stock_count' => 0,
                'out_of_stock_count' => 0,
                'reorder_needed_count' => 0,
                'avg_available_quantity' => 0,
                'stock_health_percentage' => 0
            ];
        }
    }

    /**
     * Lokasyon bazlı stok özeti getir
     */
    public function getLocationSummary(): array
    {
        try {
            $summary = DB::select("
                SELECT
                    sl.id as location_id,
                    sl.name as location_name,
                    sl.code as location_code,
                    COUNT(s.id) as total_stocks,
                    SUM(s.available_quantity) as total_available,
                    SUM(s.reserved_quantity) as total_reserved,
                    SUM(s.total_quantity) as total_inventory,
                    COUNT(CASE WHEN s.available_quantity <= s.low_stock_threshold THEN 1 END) as low_stock_count,
                    COUNT(CASE WHEN s.available_quantity <= 0 THEN 1 END) as out_of_stock_count
                FROM stock_locations sl
                LEFT JOIN stocks s ON sl.id = s.location_id AND s.is_active = 1
                WHERE sl.is_active = 1
                GROUP BY sl.id, sl.name, sl.code
                ORDER BY sl.name
            ");

            return array_map(function ($item) {
                return [
                    'location_id' => (int) $item->location_id,
                    'location_name' => $item->location_name,
                    'location_code' => $item->location_code,
                    'total_stocks' => (int) $item->total_stocks,
                    'total_available' => (int) $item->total_available,
                    'total_reserved' => (int) $item->total_reserved,
                    'total_inventory' => (int) $item->total_inventory,
                    'low_stock_count' => (int) $item->low_stock_count,
                    'out_of_stock_count' => (int) $item->out_of_stock_count,
                    'stock_health_percentage' => $item->total_stocks > 0
                        ? round((($item->total_stocks - $item->low_stock_count) / $item->total_stocks) * 100, 2)
                        : 0
                ];
            }, $summary);
        } catch (\Exception $e) {
            Log::error('Failed to get location summary', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Ürün bazlı stok özeti getir
     */
    public function getProductSummary(int $productId, ?int $productVariantId = null): array
    {
        try {
            $query = "
                SELECT
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    SUM(s.available_quantity) as total_available,
                    SUM(s.reserved_quantity) as total_reserved,
                    SUM(s.total_quantity) as total_inventory,
                    COUNT(s.id) as location_count,
                    MIN(s.available_quantity) as min_available,
                    MAX(s.available_quantity) as max_available,
                    AVG(s.available_quantity) as avg_available
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                WHERE s.product_id = ? AND s.is_active = 1
            ";

            $params = [$productId];

            if ($productVariantId) {
                $query .= " AND s.product_variant_id = ?";
                $params[] = $productVariantId;
            } else {
                $query .= " AND s.product_variant_id IS NULL";
            }

            $query .= " GROUP BY s.product_id, s.product_variant_id, p.name, p.sku, pv.sku";

            $result = DB::select($query, $params);

            if (empty($result)) {
                return [];
            }

            $summary = $result[0];

            return [
                'product_id' => (int) $summary->product_id,
                'product_variant_id' => $summary->product_variant_id ? (int) $summary->product_variant_id : null,
                'product_name' => $summary->product_name,
                'product_sku' => $summary->product_sku,
                'variant_sku' => $summary->variant_sku,
                'total_available' => (int) $summary->total_available,
                'total_reserved' => (int) $summary->total_reserved,
                'total_inventory' => (int) $summary->total_inventory,
                'location_count' => (int) $summary->location_count,
                'min_available' => (int) $summary->min_available,
                'max_available' => (int) $summary->max_available,
                'avg_available' => round((float) $summary->avg_available, 2),
                'is_in_stock' => $summary->total_available > 0,
                'stock_distribution' => $this->getStockDistribution($productId, $productVariantId)
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get product summary', [
                'product_id' => $productId,
                'product_variant_id' => $productVariantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok hareketleri getir
     */
    public function getStockMovements(int $stockId, int $limit = 50): array
    {
        try {
            $movements = DB::table('stock_movements')
                ->where('stock_id', $stockId)
                ->orderBy('movement_date', 'desc')
                ->limit($limit)
                ->get();

            return $movements->map(function ($movement) {
                return [
                    'id' => $movement->id,
                    'type' => $movement->type,
                    'reason' => $movement->reason,
                    'reference_type' => $movement->reference_type,
                    'reference_id' => $movement->reference_id,
                    'quantity' => $movement->quantity,
                    'previous_quantity' => $movement->previous_quantity,
                    'new_quantity' => $movement->new_quantity,
                    'unit_cost' => $movement->unit_cost,
                    'total_cost' => $movement->total_cost,
                    'movement_date' => $movement->movement_date,
                    'created_at' => $movement->created_at
                ];
            })->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to get stock movements', [
                'stock_id' => $stockId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Rezervasyon geçmişi getir
     */
    public function getReservationHistory(int $stockId, int $limit = 50): array
    {
        try {
            $reservations = DB::table('stock_reservations')
                ->where('stock_id', $stockId)
                ->orderBy('reserved_at', 'desc')
                ->limit($limit)
                ->get();

            return $reservations->map(function ($reservation) {
                return [
                    'id' => $reservation->id,
                    'reservation_id' => $reservation->reservation_id,
                    'reference_type' => $reservation->reference_type,
                    'reference_id' => $reservation->reference_id,
                    'quantity' => $reservation->quantity,
                    'status' => $reservation->status,
                    'reason' => $reservation->reason,
                    'reserved_at' => $reservation->reserved_at,
                    'expires_at' => $reservation->expires_at,
                    'released_at' => $reservation->released_at,
                    'fulfilled_at' => $reservation->fulfilled_at
                ];
            })->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to get reservation history', [
                'stock_id' => $stockId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Düşük stok uyarıları getir
     */
    public function getLowStockAlerts(): array
    {
        try {
            $alerts = DB::table('stock_alerts')
                ->join('stocks', 'stock_alerts.stock_id', '=', 'stocks.id')
                ->join('products', 'stocks.product_id', '=', 'products.id')
                ->leftJoin('product_variants', 'stocks.product_variant_id', '=', 'product_variants.id')
                ->leftJoin('stock_locations', 'stocks.location_id', '=', 'stock_locations.id')
                ->where('stock_alerts.status', 'active')
                ->where('stock_alerts.type', 'low_stock')
                ->select([
                    'stock_alerts.*',
                    'products.name as product_name',
                    'products.sku as product_sku',
                    'product_variants.sku as variant_sku',
                    'stock_locations.name as location_name'
                ])
                ->orderBy('stock_alerts.severity', 'desc')
                ->orderBy('stock_alerts.triggered_at', 'desc')
                ->get();

            return $alerts->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to get low stock alerts', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Yeniden sipariş önerileri getir
     */
    public function getReorderSuggestions(): array
    {
        try {
            $suggestions = DB::select("
                SELECT
                    s.id as stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    sl.name as location_name,
                    s.available_quantity,
                    s.reorder_level,
                    s.low_stock_threshold,
                    (s.reorder_level - s.available_quantity) as suggested_order_quantity,
                    CASE
                        WHEN s.available_quantity <= 0 THEN 'critical'
                        WHEN s.available_quantity <= s.low_stock_threshold THEN 'high'
                        ELSE 'medium'
                    END as priority
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                INNER JOIN stock_locations sl ON s.location_id = sl.id
                WHERE s.is_active = 1
                AND s.track_inventory = 1
                AND s.available_quantity <= s.reorder_level
                ORDER BY
                    CASE
                        WHEN s.available_quantity <= 0 THEN 1
                        WHEN s.available_quantity <= s.low_stock_threshold THEN 2
                        ELSE 3
                    END,
                    s.available_quantity ASC
            ");

            return array_map(function ($suggestion) {
                return [
                    'stock_id' => (int) $suggestion->stock_id,
                    'product_id' => (int) $suggestion->product_id,
                    'product_variant_id' => $suggestion->product_variant_id ? (int) $suggestion->product_variant_id : null,
                    'product_name' => $suggestion->product_name,
                    'product_sku' => $suggestion->product_sku,
                    'variant_sku' => $suggestion->variant_sku,
                    'location_name' => $suggestion->location_name,
                    'available_quantity' => (int) $suggestion->available_quantity,
                    'reorder_level' => (int) $suggestion->reorder_level,
                    'low_stock_threshold' => (int) $suggestion->low_stock_threshold,
                    'suggested_order_quantity' => max(1, (int) $suggestion->suggested_order_quantity),
                    'priority' => $suggestion->priority
                ];
            }, $suggestions);
        } catch (\Exception $e) {
            Log::error('Failed to get reorder suggestions', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok dağılımını getir (private helper)
     */
    private function getStockDistribution(int $productId, ?int $productVariantId): array
    {
        try {
            $query = "
                SELECT
                    sl.name as location_name,
                    sl.code as location_code,
                    s.available_quantity,
                    s.reserved_quantity,
                    s.total_quantity
                FROM stocks s
                INNER JOIN stock_locations sl ON s.location_id = sl.id
                WHERE s.product_id = ? AND s.is_active = 1
            ";

            $params = [$productId];

            if ($productVariantId) {
                $query .= " AND s.product_variant_id = ?";
                $params[] = $productVariantId;
            } else {
                $query .= " AND s.product_variant_id IS NULL";
            }

            $query .= " ORDER BY sl.name";

            $distribution = DB::select($query, $params);

            return array_map(function ($item) {
                return [
                    'location_name' => $item->location_name,
                    'location_code' => $item->location_code,
                    'available_quantity' => (int) $item->available_quantity,
                    'reserved_quantity' => (int) $item->reserved_quantity,
                    'total_quantity' => (int) $item->total_quantity
                ];
            }, $distribution);
        } catch (\Exception $e) {
            Log::error('Failed to get stock distribution', [
                'product_id' => $productId,
                'product_variant_id' => $productVariantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok değeri hesapla
     */
    public function calculateStockValue(array $productPrices = []): array
    {
        try {
            $query = "
                SELECT
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    s.total_quantity,
                    s.available_quantity,
                    s.reserved_quantity,
                    COALESCE(p.price, 0) as unit_price
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                WHERE s.is_active = 1 AND s.total_quantity > 0
                ORDER BY p.name
            ";

            $stocks = DB::select($query);
            $totalValue = 0;
            $stockValues = [];

            foreach ($stocks as $stock) {
                $key = $stock->product_id . '_' . ($stock->product_variant_id ?? 'null');

                // Özel fiyat varsa kullan, yoksa ürün fiyatını kullan
                $unitPrice = $productPrices[$key] ?? (float) $stock->unit_price;
                $stockValue = $stock->total_quantity * $unitPrice;
                $availableValue = $stock->available_quantity * $unitPrice;
                $reservedValue = $stock->reserved_quantity * $unitPrice;

                $stockValues[] = [
                    'product_id' => (int) $stock->product_id,
                    'product_variant_id' => $stock->product_variant_id ? (int) $stock->product_variant_id : null,
                    'product_name' => $stock->product_name,
                    'product_sku' => $stock->product_sku,
                    'variant_sku' => $stock->variant_sku,
                    'total_quantity' => (int) $stock->total_quantity,
                    'available_quantity' => (int) $stock->available_quantity,
                    'reserved_quantity' => (int) $stock->reserved_quantity,
                    'unit_price' => $unitPrice,
                    'total_value' => round($stockValue, 2),
                    'available_value' => round($availableValue, 2),
                    'reserved_value' => round($reservedValue, 2)
                ];

                $totalValue += $stockValue;
            }

            return [
                'total_value' => round($totalValue, 2),
                'stock_count' => count($stockValues),
                'stocks' => $stockValues
            ];
        } catch (\Exception $e) {
            Log::error('Failed to calculate stock value', [
                'error' => $e->getMessage()
            ]);
            return [
                'total_value' => 0,
                'stock_count' => 0,
                'stocks' => []
            ];
        }
    }

    /**
     * Stok devir hızı hesapla
     */
    public function calculateStockTurnover(int $days = 30): array
    {
        try {
            $startDate = Carbon::now()->subDays($days)->format('Y-m-d');
            $endDate = Carbon::now()->format('Y-m-d');

            $query = "
                SELECT
                    s.id as stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    s.available_quantity as current_stock,
                    COALESCE(SUM(CASE WHEN sm.type = 'out' THEN ABS(sm.quantity) ELSE 0 END), 0) as total_sold,
                    COALESCE(AVG(s.available_quantity), 0) as avg_inventory,
                    CASE
                        WHEN AVG(s.available_quantity) > 0
                        THEN SUM(CASE WHEN sm.type = 'out' THEN ABS(sm.quantity) ELSE 0 END) / AVG(s.available_quantity)
                        ELSE 0
                    END as turnover_ratio
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                LEFT JOIN stock_movements sm ON s.id = sm.stock_id
                    AND sm.movement_date BETWEEN ? AND ?
                    AND sm.type = 'out'
                WHERE s.is_active = 1 AND s.track_inventory = 1
                GROUP BY s.id, s.product_id, s.product_variant_id, p.name, p.sku, pv.sku, s.available_quantity
                HAVING total_sold > 0
                ORDER BY turnover_ratio DESC
            ";

            $results = DB::select($query, [$startDate, $endDate]);

            $turnovers = array_map(function ($result) use ($days) {
                $turnoverRatio = (float) $result->turnover_ratio;
                $daysInStock = $turnoverRatio > 0 ? round($days / $turnoverRatio, 1) : 0;

                return [
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'current_stock' => (int) $result->current_stock,
                    'total_sold' => (int) $result->total_sold,
                    'avg_inventory' => round((float) $result->avg_inventory, 2),
                    'turnover_ratio' => round($turnoverRatio, 4),
                    'days_in_stock' => $daysInStock,
                    'performance' => $this->getTurnoverPerformance($turnoverRatio)
                ];
            }, $results);

            return [
                'period_days' => $days,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_items' => count($turnovers),
                'avg_turnover_ratio' => count($turnovers) > 0 ? round(array_sum(array_column($turnovers, 'turnover_ratio')) / count($turnovers), 4) : 0,
                'turnovers' => $turnovers
            ];
        } catch (\Exception $e) {
            Log::error('Failed to calculate stock turnover', [
                'days' => $days,
                'error' => $e->getMessage()
            ]);
            return [
                'period_days' => $days,
                'start_date' => null,
                'end_date' => null,
                'total_items' => 0,
                'avg_turnover_ratio' => 0,
                'turnovers' => []
            ];
        }
    }

    /**
     * ABC analizi yap
     */
    public function performABCAnalysis(): array
    {
        try {
            // Son 90 günlük satış verilerini al
            $startDate = Carbon::now()->subDays(90)->format('Y-m-d');
            $endDate = Carbon::now()->format('Y-m-d');

            $query = "
                SELECT
                    s.id as stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    COALESCE(SUM(CASE WHEN sm.type = 'out' THEN ABS(sm.quantity) ELSE 0 END), 0) as total_sold,
                    COALESCE(SUM(CASE WHEN sm.type = 'out' THEN ABS(sm.quantity) * COALESCE(sm.unit_cost, p.price, 0) ELSE 0 END), 0) as total_value
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                LEFT JOIN stock_movements sm ON s.id = sm.stock_id
                    AND sm.movement_date BETWEEN ? AND ?
                    AND sm.type = 'out'
                WHERE s.is_active = 1 AND s.track_inventory = 1
                GROUP BY s.id, s.product_id, s.product_variant_id, p.name, p.sku, pv.sku
                ORDER BY total_value DESC
            ";

            $results = DB::select($query, [$startDate, $endDate]);

            if (empty($results)) {
                return [
                    'analysis_date' => Carbon::now()->format('Y-m-d'),
                    'period_days' => 90,
                    'total_items' => 0,
                    'total_value' => 0,
                    'categories' => ['A' => [], 'B' => [], 'C' => []],
                    'summary' => [
                        'A' => ['count' => 0, 'percentage' => 0, 'value_percentage' => 0],
                        'B' => ['count' => 0, 'percentage' => 0, 'value_percentage' => 0],
                        'C' => ['count' => 0, 'percentage' => 0, 'value_percentage' => 0]
                    ]
                ];
            }

            $totalValue = array_sum(array_column($results, 'total_value'));
            $totalItems = count($results);
            $cumulativeValue = 0;
            $analysis = [];

            foreach ($results as $index => $result) {
                $cumulativeValue += (float) $result->total_value;
                $valuePercentage = $totalValue > 0 ? ($result->total_value / $totalValue) * 100 : 0;
                $cumulativePercentage = $totalValue > 0 ? ($cumulativeValue / $totalValue) * 100 : 0;

                // ABC kategorisi belirle
                $category = 'C';
                if ($cumulativePercentage <= 80) {
                    $category = 'A';
                } elseif ($cumulativePercentage <= 95) {
                    $category = 'B';
                }

                $analysis[] = [
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'total_sold' => (int) $result->total_sold,
                    'total_value' => round((float) $result->total_value, 2),
                    'value_percentage' => round($valuePercentage, 2),
                    'cumulative_percentage' => round($cumulativePercentage, 2),
                    'rank' => $index + 1,
                    'abc_category' => $category
                ];
            }

            // Kategorilere göre grupla
            $categories = ['A' => [], 'B' => [], 'C' => []];
            $summary = ['A' => [], 'B' => [], 'C' => []];

            foreach ($analysis as $item) {
                $categories[$item['abc_category']][] = $item;
            }

            // Özet istatistikleri hesapla
            foreach (['A', 'B', 'C'] as $cat) {
                $catItems = $categories[$cat];
                $catValue = array_sum(array_column($catItems, 'total_value'));

                $summary[$cat] = [
                    'count' => count($catItems),
                    'percentage' => $totalItems > 0 ? round((count($catItems) / $totalItems) * 100, 2) : 0,
                    'value_percentage' => $totalValue > 0 ? round(($catValue / $totalValue) * 100, 2) : 0,
                    'total_value' => round($catValue, 2)
                ];
            }

            return [
                'analysis_date' => Carbon::now()->format('Y-m-d'),
                'period_days' => 90,
                'total_items' => $totalItems,
                'total_value' => round($totalValue, 2),
                'categories' => $categories,
                'summary' => $summary,
                'analysis' => $analysis
            ];
        } catch (\Exception $e) {
            Log::error('Failed to perform ABC analysis', [
                'error' => $e->getMessage()
            ]);
            return [
                'analysis_date' => Carbon::now()->format('Y-m-d'),
                'period_days' => 90,
                'total_items' => 0,
                'total_value' => 0,
                'categories' => ['A' => [], 'B' => [], 'C' => []],
                'summary' => [
                    'A' => ['count' => 0, 'percentage' => 0, 'value_percentage' => 0],
                    'B' => ['count' => 0, 'percentage' => 0, 'value_percentage' => 0],
                    'C' => ['count' => 0, 'percentage' => 0, 'value_percentage' => 0]
                ],
                'analysis' => []
            ];
        }
    }

    /**
     * Stok yaşlandırma raporu
     */
    public function getStockAgingReport(): array
    {
        try {
            $currentDate = Carbon::now();

            $query = "
                SELECT
                    s.id as stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    s.available_quantity,
                    s.last_movement_at,
                    COALESCE(p.price, 0) as unit_price,
                    DATEDIFF(NOW(), COALESCE(s.last_movement_at, s.created_at)) as days_since_last_movement
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                WHERE s.is_active = 1 AND s.available_quantity > 0
                ORDER BY days_since_last_movement DESC
            ";

            $results = DB::select($query);
            $agingReport = [];
            $summary = [
                '0-30' => ['count' => 0, 'quantity' => 0, 'value' => 0],
                '31-60' => ['count' => 0, 'quantity' => 0, 'value' => 0],
                '61-90' => ['count' => 0, 'quantity' => 0, 'value' => 0],
                '91-180' => ['count' => 0, 'quantity' => 0, 'value' => 0],
                '181-365' => ['count' => 0, 'quantity' => 0, 'value' => 0],
                '365+' => ['count' => 0, 'quantity' => 0, 'value' => 0]
            ];

            foreach ($results as $result) {
                $days = (int) $result->days_since_last_movement;
                $quantity = (int) $result->available_quantity;
                $unitPrice = (float) $result->unit_price;
                $value = $quantity * $unitPrice;

                // Yaş kategorisi belirle
                $ageCategory = '365+';
                if ($days <= 30) $ageCategory = '0-30';
                elseif ($days <= 60) $ageCategory = '31-60';
                elseif ($days <= 90) $ageCategory = '61-90';
                elseif ($days <= 180) $ageCategory = '91-180';
                elseif ($days <= 365) $ageCategory = '181-365';

                // Risk seviyesi belirle
                $riskLevel = 'low';
                if ($days > 365) $riskLevel = 'critical';
                elseif ($days > 180) $riskLevel = 'high';
                elseif ($days > 90) $riskLevel = 'medium';

                $agingReport[] = [
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'available_quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_value' => round($value, 2),
                    'days_since_last_movement' => $days,
                    'age_category' => $ageCategory,
                    'risk_level' => $riskLevel,
                    'last_movement_at' => $result->last_movement_at
                ];

                // Özet istatistikleri güncelle
                $summary[$ageCategory]['count']++;
                $summary[$ageCategory]['quantity'] += $quantity;
                $summary[$ageCategory]['value'] += $value;
            }

            // Özet istatistiklerini yuvarla
            foreach ($summary as $category => &$data) {
                $data['value'] = round($data['value'], 2);
            }

            return [
                'report_date' => $currentDate->format('Y-m-d'),
                'total_items' => count($agingReport),
                'total_quantity' => array_sum(array_column($agingReport, 'available_quantity')),
                'total_value' => round(array_sum(array_column($agingReport, 'total_value')), 2),
                'summary' => $summary,
                'aging_report' => $agingReport
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get stock aging report', [
                'error' => $e->getMessage()
            ]);
            return [
                'report_date' => Carbon::now()->format('Y-m-d'),
                'total_items' => 0,
                'total_quantity' => 0,
                'total_value' => 0,
                'summary' => [],
                'aging_report' => []
            ];
        }
    }

    /**
     * Kritik stok seviyesi raporu
     */
    public function getCriticalStockReport(): array
    {
        try {
            $query = "
                SELECT
                    s.id as stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    sl.name as location_name,
                    s.available_quantity,
                    s.low_stock_threshold,
                    s.reorder_level,
                    CASE
                        WHEN s.available_quantity <= 0 THEN 'out_of_stock'
                        WHEN s.available_quantity <= s.low_stock_threshold THEN 'critical'
                        WHEN s.available_quantity <= s.reorder_level THEN 'low'
                        ELSE 'normal'
                    END as status,
                    CASE
                        WHEN s.available_quantity <= 0 THEN 1
                        WHEN s.available_quantity <= s.low_stock_threshold THEN 2
                        WHEN s.available_quantity <= s.reorder_level THEN 3
                        ELSE 4
                    END as priority
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                INNER JOIN stock_locations sl ON s.location_id = sl.id
                WHERE s.is_active = 1
                AND s.track_inventory = 1
                AND s.available_quantity <= s.reorder_level
                ORDER BY priority ASC, s.available_quantity ASC
            ";

            $results = DB::select($query);

            return array_map(function ($result) {
                return [
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'location_name' => $result->location_name,
                    'available_quantity' => (int) $result->available_quantity,
                    'low_stock_threshold' => (int) $result->low_stock_threshold,
                    'reorder_level' => (int) $result->reorder_level,
                    'status' => $result->status,
                    'priority' => (int) $result->priority
                ];
            }, $results);
        } catch (\Exception $e) {
            Log::error('Failed to get critical stock report', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok hareket raporu
     */
    public function getStockMovementReport(\DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $query = "
                SELECT
                    sm.id,
                    sm.stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    sl.name as location_name,
                    sm.type,
                    sm.reason,
                    sm.reference_type,
                    sm.reference_id,
                    sm.quantity,
                    sm.previous_quantity,
                    sm.new_quantity,
                    sm.unit_cost,
                    sm.total_cost,
                    sm.movement_date,
                    sm.created_at
                FROM stock_movements sm
                INNER JOIN stocks s ON sm.stock_id = s.id
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                INNER JOIN stock_locations sl ON s.location_id = sl.id
                WHERE sm.movement_date BETWEEN ? AND ?
                ORDER BY sm.movement_date DESC, sm.created_at DESC
            ";

            $results = DB::select($query, [
                $startDate->format('Y-m-d'),
                $endDate->format('Y-m-d')
            ]);

            return array_map(function ($result) {
                return [
                    'id' => (int) $result->id,
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'location_name' => $result->location_name,
                    'type' => $result->type,
                    'reason' => $result->reason,
                    'reference_type' => $result->reference_type,
                    'reference_id' => $result->reference_id,
                    'quantity' => (int) $result->quantity,
                    'previous_quantity' => (int) $result->previous_quantity,
                    'new_quantity' => (int) $result->new_quantity,
                    'unit_cost' => $result->unit_cost ? (float) $result->unit_cost : null,
                    'total_cost' => $result->total_cost ? (float) $result->total_cost : null,
                    'movement_date' => $result->movement_date,
                    'created_at' => $result->created_at
                ];
            }, $results);
        } catch (\Exception $e) {
            Log::error('Failed to get stock movement report', [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Rezervasyon raporu
     */
    public function getReservationReport(\DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $query = "
                SELECT
                    sr.id,
                    sr.stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    sl.name as location_name,
                    sr.reservation_id,
                    sr.reference_type,
                    sr.reference_id,
                    sr.quantity,
                    sr.status,
                    sr.reason,
                    sr.reserved_at,
                    sr.expires_at,
                    sr.released_at,
                    sr.fulfilled_at
                FROM stock_reservations sr
                INNER JOIN stocks s ON sr.stock_id = s.id
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                INNER JOIN stock_locations sl ON s.location_id = sl.id
                WHERE sr.reserved_at BETWEEN ? AND ?
                ORDER BY sr.reserved_at DESC
            ";

            $results = DB::select($query, [
                $startDate->format('Y-m-d H:i:s'),
                $endDate->format('Y-m-d H:i:s')
            ]);

            return array_map(function ($result) {
                return [
                    'id' => (int) $result->id,
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'location_name' => $result->location_name,
                    'reservation_id' => $result->reservation_id,
                    'reference_type' => $result->reference_type,
                    'reference_id' => $result->reference_id,
                    'quantity' => (int) $result->quantity,
                    'status' => $result->status,
                    'reason' => $result->reason,
                    'reserved_at' => $result->reserved_at,
                    'expires_at' => $result->expires_at,
                    'released_at' => $result->released_at,
                    'fulfilled_at' => $result->fulfilled_at
                ];
            }, $results);
        } catch (\Exception $e) {
            Log::error('Failed to get reservation report', [
                'start_date' => $startDate->format('Y-m-d H:i:s'),
                'end_date' => $endDate->format('Y-m-d H:i:s'),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok performans raporu
     */
    public function getStockPerformanceReport(): array
    {
        try {
            $query = "
                SELECT
                    s.id as stock_id,
                    s.product_id,
                    s.product_variant_id,
                    p.name as product_name,
                    p.sku as product_sku,
                    pv.sku as variant_sku,
                    sl.name as location_name,
                    s.available_quantity,
                    s.reserved_quantity,
                    s.total_quantity,
                    s.low_stock_threshold,
                    s.reorder_level,
                    COALESCE(SUM(CASE WHEN sm.type = 'in' THEN sm.quantity ELSE 0 END), 0) as total_received,
                    COALESCE(SUM(CASE WHEN sm.type = 'out' THEN ABS(sm.quantity) ELSE 0 END), 0) as total_sold,
                    COALESCE(COUNT(CASE WHEN sm.type = 'out' THEN 1 END), 0) as sales_frequency,
                    CASE
                        WHEN s.available_quantity <= 0 THEN 'critical'
                        WHEN s.available_quantity <= s.low_stock_threshold THEN 'low'
                        WHEN s.available_quantity <= s.reorder_level THEN 'medium'
                        ELSE 'good'
                    END as stock_status,
                    CASE
                        WHEN COUNT(CASE WHEN sm.type = 'out' THEN 1 END) >= 10 THEN 'high'
                        WHEN COUNT(CASE WHEN sm.type = 'out' THEN 1 END) >= 5 THEN 'medium'
                        WHEN COUNT(CASE WHEN sm.type = 'out' THEN 1 END) >= 1 THEN 'low'
                        ELSE 'none'
                    END as sales_performance
                FROM stocks s
                INNER JOIN products p ON s.product_id = p.id
                LEFT JOIN product_variants pv ON s.product_variant_id = pv.id
                INNER JOIN stock_locations sl ON s.location_id = sl.id
                LEFT JOIN stock_movements sm ON s.id = sm.stock_id
                    AND sm.movement_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                WHERE s.is_active = 1 AND s.track_inventory = 1
                GROUP BY s.id, s.product_id, s.product_variant_id, p.name, p.sku, pv.sku, sl.name,
                         s.available_quantity, s.reserved_quantity, s.total_quantity,
                         s.low_stock_threshold, s.reorder_level
                ORDER BY total_sold DESC, sales_frequency DESC
            ";

            $results = DB::select($query);

            return array_map(function ($result) {
                return [
                    'stock_id' => (int) $result->stock_id,
                    'product_id' => (int) $result->product_id,
                    'product_variant_id' => $result->product_variant_id ? (int) $result->product_variant_id : null,
                    'product_name' => $result->product_name,
                    'product_sku' => $result->product_sku,
                    'variant_sku' => $result->variant_sku,
                    'location_name' => $result->location_name,
                    'available_quantity' => (int) $result->available_quantity,
                    'reserved_quantity' => (int) $result->reserved_quantity,
                    'total_quantity' => (int) $result->total_quantity,
                    'low_stock_threshold' => (int) $result->low_stock_threshold,
                    'reorder_level' => (int) $result->reorder_level,
                    'total_received' => (int) $result->total_received,
                    'total_sold' => (int) $result->total_sold,
                    'sales_frequency' => (int) $result->sales_frequency,
                    'stock_status' => $result->stock_status,
                    'sales_performance' => $result->sales_performance,
                    'stock_efficiency' => $this->calculateStockEfficiency(
                        (int) $result->available_quantity,
                        (int) $result->total_sold,
                        (int) $result->sales_frequency
                    )
                ];
            }, $results);
        } catch (\Exception $e) {
            Log::error('Failed to get stock performance report', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Stok verimliliğini hesapla (private helper)
     */
    private function calculateStockEfficiency(int $availableQuantity, int $totalSold, int $salesFrequency): string
    {
        if ($totalSold === 0) return 'no_sales';

        $turnoverRatio = $availableQuantity > 0 ? $totalSold / $availableQuantity : 0;

        if ($turnoverRatio >= 2 && $salesFrequency >= 10) return 'excellent';
        if ($turnoverRatio >= 1 && $salesFrequency >= 5) return 'good';
        if ($turnoverRatio >= 0.5 && $salesFrequency >= 2) return 'average';
        if ($turnoverRatio >= 0.1) return 'poor';
        return 'very_poor';
    }

    /**
     * Turnover performansını değerlendir (private helper)
     */
    private function getTurnoverPerformance(float $turnoverRatio): string
    {
        if ($turnoverRatio >= 12) return 'excellent';
        if ($turnoverRatio >= 6) return 'good';
        if ($turnoverRatio >= 3) return 'average';
        if ($turnoverRatio >= 1) return 'poor';
        return 'very_poor';
    }
}