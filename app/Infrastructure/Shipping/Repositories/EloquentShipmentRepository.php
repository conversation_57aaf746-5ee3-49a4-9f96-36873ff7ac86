<?php

namespace App\Infrastructure\Shipping\Repositories;

use App\Domain\Shipping\Entities\Shipment;
use App\Domain\Shipping\Repositories\ShipmentRepositoryInterface;
use App\Domain\Shipping\ValueObjects\TrackingNumber;
use App\Domain\Shipping\ValueObjects\DeliveryStatus;
use App\Infrastructure\Shipping\Models\EloquentShipment;
use App\Infrastructure\Shipping\Models\EloquentCarrierIntegration;
use App\Infrastructure\Shipping\Mappers\ShipmentMapper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * EloquentShipmentRepository
 * Eloquent tabanlı shipment repository implementasyonu
 */
class EloquentShipmentRepository implements ShipmentRepositoryInterface
{
    public function __construct(
        private ShipmentMapper $mapper
    ) {}

    /**
     * ID ile shipment bul
     */
    public function findById(int $id): ?Shipment
    {
        try {
            $eloquentShipment = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->find($id);

            return $eloquentShipment ? $this->mapper->toDomain($eloquentShipment) : null;
        } catch (\Exception $e) {
            Log::error('Failed to find shipment by ID', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Shipment number ile bul
     */
    public function findByShipmentNumber(string $shipmentNumber): ?Shipment
    {
        try {
            $eloquentShipment = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('shipment_number', $shipmentNumber)->first();

            return $eloquentShipment ? $this->mapper->toDomain($eloquentShipment) : null;
        } catch (\Exception $e) {
            Log::error('Failed to find shipment by number', [
                'shipment_number' => $shipmentNumber,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Tracking number ile bul
     */
    public function findByTrackingNumber(TrackingNumber $trackingNumber): ?Shipment
    {
        try {
            $eloquentShipment = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('tracking_number', $trackingNumber->getValue())->first();

            return $eloquentShipment ? $this->mapper->toDomain($eloquentShipment) : null;
        } catch (\Exception $e) {
            Log::error('Failed to find shipment by tracking number', [
                'tracking_number' => $trackingNumber->getValue(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Order ID ile shipment'ları bul
     */
    public function findByOrderId(int $orderId): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('order_id', $orderId)->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find shipments by order ID', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Status ile shipment'ları bul
     */
    public function findByStatus(string $status): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('status', $status)->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find shipments by status', [
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Carrier ile shipment'ları bul
     */
    public function findByCarrier(string $carrierCode): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->whereHas('carrierIntegration', function ($query) use ($carrierCode) {
                $query->where('carrier_code', $carrierCode);
            })->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find shipments by carrier', [
                'carrier_code' => $carrierCode,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Tarih aralığında shipment'ları bul
     */
    public function findByDateRange(\DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->whereBetween('created_at', [$startDate, $endDate])->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find shipments by date range', [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Pending shipment'ları bul
     */
    public function findPendingShipments(): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->whereIn('status', ['pending', 'processing'])->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find pending shipments', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * In transit shipment'ları bul
     */
    public function findInTransitShipments(): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->whereIn('status', ['shipped', 'in_transit', 'out_for_delivery'])->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find in transit shipments', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Delayed shipment'ları bul
     */
    public function findDelayedShipments(): array
    {
        try {
            $now = Carbon::now();

            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('estimated_delivery_at', '<', $now)
              ->whereNotIn('status', ['delivered', 'cancelled', 'failed'])
              ->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find delayed shipments', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Failed delivery shipment'ları bul
     */
    public function findFailedDeliveries(): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('status', 'failed')
              ->orWhere('delivery_attempts', '>', 0)
              ->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find failed deliveries', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Tracking update gereken shipment'ları bul
     */
    public function findNeedingTrackingUpdate(int $maxAgeHours = 24): array
    {
        try {
            $cutoffTime = Carbon::now()->subHours($maxAgeHours);

            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->whereNotIn('status', ['delivered', 'cancelled', 'failed'])
              ->where(function ($query) use ($cutoffTime) {
                  $query->whereNull('last_tracking_update')
                        ->orWhere('last_tracking_update', '<', $cutoffTime);
              })
              ->whereNotNull('tracking_number')
              ->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find shipments needing tracking update', [
                'max_age_hours' => $maxAgeHours,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Shipment kaydet
     */
    public function save(Shipment $shipment): void
    {
        try {
            DB::transaction(function () use ($shipment) {
                $eloquentShipment = $this->mapper->toEloquent($shipment);
                $eloquentShipment->save();

                // ID'yi domain entity'ye set et
                if (!$shipment->getId()) {
                    $shipment->setId($eloquentShipment->id);
                }
            });

            Log::info('Shipment saved successfully', [
                'shipment_id' => $shipment->getId(),
                'shipment_number' => $shipment->getShipmentNumber(),
                'order_id' => $shipment->getOrderId(),
                'status' => $shipment->getStatus()->getValue()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to save shipment', [
                'shipment_id' => $shipment->getId(),
                'order_id' => $shipment->getOrderId(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Shipment sil
     */
    public function delete(Shipment $shipment): void
    {
        try {
            DB::transaction(function () use ($shipment) {
                EloquentShipment::where('id', $shipment->getId())->delete();
            });

            Log::info('Shipment deleted successfully', [
                'shipment_id' => $shipment->getId(),
                'shipment_number' => $shipment->getShipmentNumber()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete shipment', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Toplu shipment kaydet
     */
    public function saveBatch(array $shipments): void
    {
        try {
            DB::transaction(function () use ($shipments) {
                foreach ($shipments as $shipment) {
                    $this->save($shipment);
                }
            });

            Log::info('Batch shipment save completed', [
                'count' => count($shipments)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to save shipment batch', [
                'count' => count($shipments),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Shipment sayısını getir
     */
    public function count(): int
    {
        try {
            return EloquentShipment::count();
        } catch (\Exception $e) {
            Log::error('Failed to count shipments', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Sayfalı shipment listesi getir
     */
    public function paginate(int $page = 1, int $perPage = 20, array $filters = []): array
    {
        try {
            $query = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents' => function ($q) {
                    $q->orderBy('event_time', 'desc')->limit(5);
                }
            ]);

            // Filtreleri uygula
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['carrier_code'])) {
                $query->whereHas('carrierIntegration', function ($q) use ($filters) {
                    $q->where('carrier_code', $filters['carrier_code']);
                });
            }

            if (!empty($filters['order_id'])) {
                $query->where('order_id', $filters['order_id']);
            }

            if (!empty($filters['tracking_number'])) {
                $query->where('tracking_number', 'like', "%{$filters['tracking_number']}%");
            }

            if (!empty($filters['date_from'])) {
                $query->where('created_at', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->where('created_at', '<=', $filters['date_to']);
            }

            $total = $query->count();
            $shipments = $query->skip(($page - 1) * $perPage)
                              ->take($perPage)
                              ->orderBy('created_at', 'desc')
                              ->get();

            return [
                'data' => $shipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray(),
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => min($page * $perPage, $total)
            ];
        } catch (\Exception $e) {
            Log::error('Failed to paginate shipments', [
                'page' => $page,
                'per_page' => $perPage,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'data' => [],
                'total' => 0,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => 0,
                'from' => 0,
                'to' => 0
            ];
        }
    }

    /**
     * Shipment arama
     */
    public function search(string $query, array $filters = []): array
    {
        try {
            $searchQuery = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents' => function ($q) {
                    $q->orderBy('event_time', 'desc')->limit(3);
                }
            ]);

            // Arama terimi ile eşleşen alanları ara
            $searchQuery->where(function ($q) use ($query) {
                $q->where('shipment_number', 'like', "%{$query}%")
                  ->orWhere('tracking_number', 'like', "%{$query}%")
                  ->orWhere('carrier_tracking_number', 'like', "%{$query}%")
                  ->orWhere('reference_number', 'like', "%{$query}%")
                  ->orWhereJsonContains('destination_address->name', $query)
                  ->orWhereJsonContains('destination_address->company', $query);
            });

            // Filtreleri uygula
            if (!empty($filters['status'])) {
                $searchQuery->where('status', $filters['status']);
            }

            if (!empty($filters['carrier_code'])) {
                $searchQuery->whereHas('carrierIntegration', function ($q) use ($filters) {
                    $q->where('carrier_code', $filters['carrier_code']);
                });
            }

            $shipments = $searchQuery->limit(50)->get();

            return $shipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to search shipments', [
                'query' => $query,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Shipment istatistikleri getir
     */
    public function getStatistics(): array
    {
        try {
            $stats = DB::select("
                SELECT
                    COUNT(*) as total_shipments,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_count,
                    COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_count,
                    COUNT(CASE WHEN status = 'in_transit' THEN 1 END) as in_transit_count,
                    COUNT(CASE WHEN status = 'out_for_delivery' THEN 1 END) as out_for_delivery_count,
                    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_count,
                    COUNT(CASE WHEN estimated_delivery_at < NOW() AND status NOT IN ('delivered', 'cancelled', 'failed') THEN 1 END) as delayed_count,
                    AVG(CASE WHEN actual_delivery_at IS NOT NULL AND shipped_at IS NOT NULL
                        THEN TIMESTAMPDIFF(HOUR, shipped_at, actual_delivery_at) END) as avg_delivery_hours,
                    SUM(total_cost) as total_shipping_cost,
                    AVG(total_cost) as avg_shipping_cost
                FROM shipments
                WHERE deleted_at IS NULL
            ");

            $carriersStats = DB::select("
                SELECT
                    ci.carrier_code,
                    ci.carrier_name,
                    COUNT(s.id) as shipment_count,
                    COUNT(CASE WHEN s.status = 'delivered' THEN 1 END) as delivered_count,
                    AVG(CASE WHEN s.actual_delivery_at IS NOT NULL AND s.shipped_at IS NOT NULL
                        THEN TIMESTAMPDIFF(HOUR, s.shipped_at, s.actual_delivery_at) END) as avg_delivery_hours,
                    SUM(s.total_cost) as total_cost
                FROM carrier_integrations ci
                LEFT JOIN shipments s ON ci.id = s.carrier_integration_id AND s.deleted_at IS NULL
                WHERE ci.is_active = 1
                GROUP BY ci.id, ci.carrier_code, ci.carrier_name
                ORDER BY shipment_count DESC
            ");

            return [
                'total_shipments' => (int) $stats[0]->total_shipments,
                'pending_count' => (int) $stats[0]->pending_count,
                'processing_count' => (int) $stats[0]->processing_count,
                'shipped_count' => (int) $stats[0]->shipped_count,
                'in_transit_count' => (int) $stats[0]->in_transit_count,
                'out_for_delivery_count' => (int) $stats[0]->out_for_delivery_count,
                'delivered_count' => (int) $stats[0]->delivered_count,
                'failed_count' => (int) $stats[0]->failed_count,
                'cancelled_count' => (int) $stats[0]->cancelled_count,
                'delayed_count' => (int) $stats[0]->delayed_count,
                'avg_delivery_hours' => round((float) $stats[0]->avg_delivery_hours, 2),
                'total_shipping_cost' => round((float) $stats[0]->total_shipping_cost, 2),
                'avg_shipping_cost' => round((float) $stats[0]->avg_shipping_cost, 2),
                'delivery_success_rate' => $stats[0]->total_shipments > 0
                    ? round(($stats[0]->delivered_count / $stats[0]->total_shipments) * 100, 2)
                    : 0,
                'carriers' => array_map(function ($carrier) {
                    return [
                        'carrier_code' => $carrier->carrier_code,
                        'carrier_name' => $carrier->carrier_name,
                        'shipment_count' => (int) $carrier->shipment_count,
                        'delivered_count' => (int) $carrier->delivered_count,
                        'avg_delivery_hours' => round((float) $carrier->avg_delivery_hours, 2),
                        'total_cost' => round((float) $carrier->total_cost, 2),
                        'delivery_rate' => $carrier->shipment_count > 0
                            ? round(($carrier->delivered_count / $carrier->shipment_count) * 100, 2)
                            : 0
                    ];
                }, $carriersStats)
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get shipment statistics', [
                'error' => $e->getMessage()
            ]);
            return [
                'total_shipments' => 0,
                'pending_count' => 0,
                'processing_count' => 0,
                'shipped_count' => 0,
                'in_transit_count' => 0,
                'out_for_delivery_count' => 0,
                'delivered_count' => 0,
                'failed_count' => 0,
                'cancelled_count' => 0,
                'delayed_count' => 0,
                'avg_delivery_hours' => 0,
                'total_shipping_cost' => 0,
                'avg_shipping_cost' => 0,
                'delivery_success_rate' => 0,
                'carriers' => []
            ];
        }
    }

    /**
     * Order ID ile shipment'ları bul
     */
    public function findByOrderId(int $orderId): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('order_id', $orderId)->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find shipments by order ID', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Adres ile shipment'ları bul
     */
    public function findByAddress(ShippingAddress $address): array
    {
        // Bu metod için basit bir implementasyon
        return [];
    }

    /**
     * Aktif shipment'ları bul
     */
    public function findActive(): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->whereNotIn('status', ['delivered', 'cancelled', 'failed'])->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find active shipments', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Teslim edilmiş shipment'ları bul
     */
    public function findDelivered(): array
    {
        try {
            $eloquentShipments = EloquentShipment::with([
                'carrierIntegration',
                'trackingEvents',
                'deliveryAttempts',
                'labels'
            ])->where('status', 'delivered')->get();

            return $eloquentShipments->map(fn($shipment) => $this->mapper->toDomain($shipment))->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to find delivered shipments', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Bekleyen shipment'ları bul
     */
    public function findPending(): array
    {
        return $this->findPendingShipments();
    }

    /**
     * Carrier bazlı özet getir
     */
    public function getCarrierSummary(): array
    {
        return [];
    }

    /**
     * Status bazlı özet getir
     */
    public function getStatusSummary(): array
    {
        return [];
    }

    /**
     * Tracking geçmişi getir
     */
    public function getTrackingHistory(int $shipmentId): array
    {
        return [];
    }

    /**
     * Delivery attempts getir
     */
    public function getDeliveryAttempts(int $shipmentId): array
    {
        return [];
    }

    /**
     * Shipment notları getir
     */
    public function getNotes(int $shipmentId): array
    {
        return [];
    }

    /**
     * Geciken shipment'ları getir
     */
    public function getDelayedShipments(): array
    {
        return $this->findDelayedShipments();
    }

    /**
     * Performans raporu getir
     */
    public function getPerformanceReport(\DateTime $startDate, \DateTime $endDate): array
    {
        return [];
    }

    /**
     * Maliyet analizi getir
     */
    public function getCostAnalysis(\DateTime $startDate, \DateTime $endDate): array
    {
        return [];
    }

    /**
     * Carrier performans karşılaştırması getir
     */
    public function getCarrierComparison(\DateTime $startDate, \DateTime $endDate): array
    {
        return [];
    }
}