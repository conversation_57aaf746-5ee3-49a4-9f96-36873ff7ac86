<?php

namespace App\Infrastructure\Notifications\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * NotificationAnalyticsService
 * Bildirim analitik hizmetleri
 */
class NotificationAnalyticsService
{
    /**
     * Kanal istatistiklerini getir
     */
    public function getChannelStatistics(string $channel = null): array
    {
        $query = DB::table('notifications');

        if ($channel) {
            $query->where('channel', $channel);
        }

        return [
            'total_sent' => $query->count(),
            'total_delivered' => $query->where('status', 'delivered')->count(),
            'total_failed' => $query->where('status', 'failed')->count(),
            'total_pending' => $query->where('status', 'pending')->count(),
            'delivery_rate' => $this->calculateDeliveryRate($channel),
        ];
    }

    /**
     * Teslimat oranını hesapla
     */
    public function calculateDeliveryRate(string $channel = null): float
    {
        $query = DB::table('notifications');

        if ($channel) {
            $query->where('channel', $channel);
        }

        $total = $query->count();
        $delivered = $query->where('status', 'delivered')->count();

        return $total > 0 ? ($delivered / $total) * 100 : 0.0;
    }

    /**
     * Kanal sağlığını kontrol et
     */
    public function checkChannelHealth(string $channel): array
    {
        $cacheKey = "channel_health_{$channel}";

        return Cache::remember($cacheKey, 300, function () use ($channel) {
            $stats = $this->getChannelStatistics($channel);
            $recentFailures = $this->getRecentFailures($channel);

            return [
                'channel' => $channel,
                'status' => $this->determineHealthStatus($stats, $recentFailures),
                'delivery_rate' => $stats['delivery_rate'],
                'recent_failures' => $recentFailures,
                'last_check' => now()->toISOString(),
            ];
        });
    }

    /**
     * Son hataları getir
     */
    public function getRecentFailures(string $channel, int $hours = 24): int
    {
        return DB::table('notifications')
            ->where('channel', $channel)
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subHours($hours))
            ->count();
    }

    /**
     * Sağlık durumunu belirle
     */
    private function determineHealthStatus(array $stats, int $recentFailures): string
    {
        if ($stats['delivery_rate'] >= 95 && $recentFailures < 10) {
            return 'healthy';
        } elseif ($stats['delivery_rate'] >= 80 && $recentFailures < 50) {
            return 'warning';
        } else {
            return 'critical';
        }
    }

    /**
     * Kapsamlı istatistikleri getir
     */
    public function getComprehensiveStatistics(): array
    {
        return [
            'overview' => $this->getChannelStatistics(),
            'by_channel' => $this->getStatisticsByChannel(),
            'recent_activity' => $this->getRecentActivity(),
            'performance_metrics' => $this->getPerformanceMetrics(),
        ];
    }

    /**
     * Kanallara göre istatistikleri getir
     */
    public function getStatisticsByChannel(): array
    {
        $channels = ['email', 'sms', 'push', 'database'];
        $stats = [];

        foreach ($channels as $channel) {
            $stats[$channel] = $this->getChannelStatistics($channel);
        }

        return $stats;
    }

    /**
     * Son aktiviteyi getir
     */
    public function getRecentActivity(int $hours = 24): array
    {
        return DB::table('notifications')
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered'),
                DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed')
            )
            ->where('created_at', '>=', now()->subHours($hours))
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Performans metriklerini getir
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'average_delivery_time' => $this->getAverageDeliveryTime(),
            'peak_hours' => $this->getPeakHours(),
            'failure_reasons' => $this->getFailureReasons(),
        ];
    }

    /**
     * Ortalama teslimat süresini getir
     */
    public function getAverageDeliveryTime(): float
    {
        // Basit implementasyon - gerçek uygulamada delivery_time kolonu gerekir
        return 2.5; // saniye
    }

    /**
     * Yoğun saatleri getir
     */
    public function getPeakHours(): array
    {
        return DB::table('notifications')
            ->select(
                DB::raw('HOUR(created_at) as hour'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    /**
     * Hata nedenlerini getir
     */
    public function getFailureReasons(): array
    {
        // Basit implementasyon - gerçek uygulamada failure_reason kolonu gerekir
        return [
            'network_timeout' => 15,
            'invalid_recipient' => 8,
            'service_unavailable' => 5,
            'rate_limit_exceeded' => 3,
            'other' => 2,
        ];
    }

    /**
     * Bildirim olayını kaydet
     */
    public function recordEvent(string $event, array $data = []): void
    {
        // Event logging implementasyonu
        DB::table('notification_events')->insert([
            'event' => $event,
            'data' => json_encode($data),
            'created_at' => now(),
        ]);
    }

    /**
     * Başarısız bildirimleri yeniden dene
     */
    public function retryFailedNotifications(string $channel = null, int $maxRetries = 3): int
    {
        $query = DB::table('notifications')
            ->where('status', 'failed')
            ->where('retry_count', '<', $maxRetries);

        if ($channel) {
            $query->where('channel', $channel);
        }

        $failedNotifications = $query->get();
        $retriedCount = 0;

        foreach ($failedNotifications as $notification) {
            // Retry logic burada implement edilecek
            DB::table('notifications')
                ->where('id', $notification->id)
                ->update([
                    'status' => 'pending',
                    'retry_count' => $notification->retry_count + 1,
                    'updated_at' => now(),
                ]);

            $retriedCount++;
        }

        return $retriedCount;
    }

    /**
     * Eski bildirimleri temizle
     */
    public function cleanupOldNotifications(int $days = 30): int
    {
        return DB::table('notifications')
            ->where('created_at', '<', now()->subDays($days))
            ->delete();
    }
}
