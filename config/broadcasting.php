<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Broadcaster
    |--------------------------------------------------------------------------
    |
    | This option controls the default broadcaster that will be used by the
    | framework when an event needs to be broadcast. You may set this to
    | any of the connections defined in the "connections" array below.
    |
    | Supported: "reverb", "pusher", "ably", "redis", "log", "null"
    |
    */

    'default' => env('BROADCAST_CONNECTION', 'reverb'),

    /*
    |--------------------------------------------------------------------------
    | Broadcast Connections
    |--------------------------------------------------------------------------
    |
    | Here you may define all of the broadcast connections that will be used
    | to broadcast events to other systems or over websockets. Samples of
    | each available type of connection are provided inside this array.
    |
    */

    'connections' => [

        'reverb' => [
            'driver' => 'reverb',
            'key' => env('REVERB_APP_KEY'),
            'secret' => env('REVERB_APP_SECRET'),
            'app_id' => env('REVERB_APP_ID'),
            'options' => [
                'host' => env('REVERB_HOST', '127.0.0.1'),
                'port' => env('REVERB_PORT', 8080),
                'scheme' => env('REVERB_SCHEME', 'http'),
                'useTLS' => env('REVERB_SCHEME', 'http') === 'https',
            ],
            'client_options' => [
                // Guzzle client options: https://docs.guzzlephp.org/en/stable/request-options.html
            ],
        ],

        'pusher' => [
            'driver' => 'pusher',
            'key' => env('PUSHER_APP_KEY', 'test-key'),
            'secret' => env('PUSHER_APP_SECRET', 'test-secret'),
            'app_id' => env('PUSHER_APP_ID', 'test-app-id'),
            'options' => [
                'cluster' => env('PUSHER_APP_CLUSTER', 'mt1'),
                'host' => env('PUSHER_HOST') ?: 'api-'.env('PUSHER_APP_CLUSTER', 'mt1').'.pusherapp.com',
                'port' => env('PUSHER_PORT', 443),
                'scheme' => env('PUSHER_SCHEME', 'https'),
                'encrypted' => true,
                'useTLS' => env('PUSHER_SCHEME', 'https') === 'https',
            ],
            'client_options' => [
                // Guzzle client options: https://docs.guzzlephp.org/en/stable/request-options.html
            ],
        ],

        'ably' => [
            'driver' => 'ably',
            'key' => env('ABLY_KEY'),
        ],

        'log' => [
            'driver' => 'log',
        ],

        'null' => [
            'driver' => 'null',
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => env('BROADCAST_REDIS_CONNECTION', 'default'),
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Real-Time Features Configuration
    |--------------------------------------------------------------------------
    |
    | ModularEcommerce real-time özellik konfigürasyonu
    |
    */

    'features' => [
        'enabled' => env('REALTIME_ENABLED', true),
        'debug' => env('REALTIME_DEBUG', false),
        
        // Hangi event'lerin real-time broadcast edileceği
        'broadcast_events' => [
            'product_stock_updated' => env('REALTIME_PRODUCT_STOCK', true),
            'product_price_changed' => env('REALTIME_PRODUCT_PRICE', true),
            'order_status_changed' => env('REALTIME_ORDER_STATUS', true),
            'cart_updated' => env('REALTIME_CART_UPDATES', true),
            'notifications' => env('REALTIME_NOTIFICATIONS', true),
            'admin_alerts' => env('REALTIME_ADMIN_ALERTS', true),
        ],

        // Channel authorization
        'auth' => [
            'enabled' => env('REALTIME_AUTH_ENABLED', true),
            'middleware' => ['auth:sanctum'],
            'guards' => ['web', 'api'],
        ],

        // Performance settings
        'performance' => [
            'max_connections_per_user' => env('REALTIME_MAX_CONNECTIONS', 5),
            'connection_timeout' => env('REALTIME_CONNECTION_TIMEOUT', 60),
            'heartbeat_interval' => env('REALTIME_HEARTBEAT_INTERVAL', 30),
            'max_message_size' => env('REALTIME_MAX_MESSAGE_SIZE', 1024), // KB
        ],

        // Rate limiting
        'rate_limiting' => [
            'enabled' => env('REALTIME_RATE_LIMITING', true),
            'max_events_per_minute' => env('REALTIME_MAX_EVENTS_PER_MINUTE', 60),
            'max_events_per_hour' => env('REALTIME_MAX_EVENTS_PER_HOUR', 1000),
        ],

        // Monitoring
        'monitoring' => [
            'enabled' => env('REALTIME_MONITORING', true),
            'log_connections' => env('REALTIME_LOG_CONNECTIONS', false),
            'log_events' => env('REALTIME_LOG_EVENTS', false),
            'metrics_enabled' => env('REALTIME_METRICS', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Channel Prefixes
    |--------------------------------------------------------------------------
    |
    | Channel prefix'leri organizasyon için
    |
    */

    'channel_prefixes' => [
        'public' => 'public',
        'private' => 'private',
        'presence' => 'presence',
        'admin' => 'admin',
        'user' => 'user',
        'product' => 'product',
        'order' => 'order',
        'cart' => 'cart',
        'notification' => 'notification',
    ],

];
